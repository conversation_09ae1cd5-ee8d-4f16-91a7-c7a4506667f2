{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f1213fa2132feda34da2afc04866d1e4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98626ba2350edf76f8425a034f3e46f5ab", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9809ca34579abe135a66f6b9df15bd0a42", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a22c832f06ea3ceef2cad28da8a0856", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9809ca34579abe135a66f6b9df15bd0a42", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b372839d6de297dbb35b5d848ee35b94", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a9091d952eb3ce72e0df0aa793f687a7", "guid": "bfdfe7dc352907fc980b868725387e98d78fc46a1f37dee5cd963c12fcade056"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3efa0c72a72297613925306ad02f73c", "guid": "bfdfe7dc352907fc980b868725387e98af1d49e4181e922d38142e58a0e4cdb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98485a045c43157d3f4f751b2555b43805", "guid": "bfdfe7dc352907fc980b868725387e98df893105daa3dcc24550107f36232acd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892b12ce4e02df6618750813b50654ba6", "guid": "bfdfe7dc352907fc980b868725387e98f7ccc605c0b4515a19b77e99f80d805f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a169712b82800f173e7a07bf30e0825b", "guid": "bfdfe7dc352907fc980b868725387e9829e69f64f38d5822a7e9a5dd0d4f1ab0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988520acb1f733672c1bdfd464b9a9b294", "guid": "bfdfe7dc352907fc980b868725387e98f78c46d591b5802ade08773299702dd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcfd6a8e2dcc5a902077645d1e381c1b", "guid": "bfdfe7dc352907fc980b868725387e982ca55ea42ff6820835249c10baa9dd5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985047f927bb3466bfa7652f0de88f36c8", "guid": "bfdfe7dc352907fc980b868725387e98c5e5739d20b317e29d4b9ee26573ca40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98740e576f0c0111df13d3e73ae519c7c7", "guid": "bfdfe7dc352907fc980b868725387e985bafb64f892ebdc10bb7f3f8ce743c31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d41f22132de28d3972e9eb76a0d61085", "guid": "bfdfe7dc352907fc980b868725387e98a3bd7eb62356bb1917571f8f80a1eb45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874f65433f0cb2c8facc8258c4a051c28", "guid": "bfdfe7dc352907fc980b868725387e98db7c06afae201797ecf277cca9488feb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c02463e36c00d4005ccf74c4c5a4160", "guid": "bfdfe7dc352907fc980b868725387e98e06735d94dc4361a59ebb795b8599202"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6bae0a53875e15d173e9d29a6485440", "guid": "bfdfe7dc352907fc980b868725387e98635d79af2de1375bf1d711bf52384d71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814d2496b6c42d42d2d208779dc9314cf", "guid": "bfdfe7dc352907fc980b868725387e98cc3797e5230a7bda18abae1f3b165929"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877a5b9601bee6b7c7376429d7aa6608c", "guid": "bfdfe7dc352907fc980b868725387e98929b2c26a00d843dd991e22e85dd230b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef0861ea3427e5dcfb9377067f11862b", "guid": "bfdfe7dc352907fc980b868725387e982931e31832ecbe4f5cce63f45ee5ca35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d7dfd5c112451b187fa6d9600bb9a10", "guid": "bfdfe7dc352907fc980b868725387e981f7024d4b6da74b1841221a710e3e644"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ea5716c01eaad3426864193cac33a91", "guid": "bfdfe7dc352907fc980b868725387e98130e045e9bd1de280a963d559d6296c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a2365a16906a3ae62ec81ed2898028b", "guid": "bfdfe7dc352907fc980b868725387e98b74eb38f461fd6708dcde2763a316a14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980229835c92a24d75357b9867aee163ec", "guid": "bfdfe7dc352907fc980b868725387e983a6e15bfcc93db3117d26f2ed2d7fd5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98040986ef31925d66f9cac370af756128", "guid": "bfdfe7dc352907fc980b868725387e98f7a5597dcc90522bc9702e97c14dc3da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d51f2abb5e89d800b8b9f38a89852ccd", "guid": "bfdfe7dc352907fc980b868725387e98a9dc7393c621607cb4d5acbf402ac7d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864005895c2c3ce7ae90e6101356429d9", "guid": "bfdfe7dc352907fc980b868725387e98be8b3a469f7172b5fa67f293f5c31f27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984907cf0083fec12577d389d85647a686", "guid": "bfdfe7dc352907fc980b868725387e98b4efbf597d56f21c78a5cff8a3f92617"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e172e9658c9607b7461bb9348414d545", "guid": "bfdfe7dc352907fc980b868725387e9889a80ada7296e78e0ecdc5be592bcd94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98339f8d37df6023a37548e4f6e75d0561", "guid": "bfdfe7dc352907fc980b868725387e98a97218091a376c8e78f9698ea0960890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98633b2a8816b9f47ea65a291066b20170", "guid": "bfdfe7dc352907fc980b868725387e98e1c2758c6d3a38eb33641b22505ee91e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897e0f35ee8d95246d6b7c1e063eca37e", "guid": "bfdfe7dc352907fc980b868725387e9814fc8a7f6e702d536cc1b9904cc94356"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f01e97b5c446e5716232e35a0d66ee1", "guid": "bfdfe7dc352907fc980b868725387e9848ccaf31f789e0d973302083174ddd63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d43eb979b52979b0f19d558163ee424c", "guid": "bfdfe7dc352907fc980b868725387e98c7f02c6bdb2e112164ff6b8f1162de3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a7f48ac4446005ed5208befc7f5e1d5", "guid": "bfdfe7dc352907fc980b868725387e98f6f9f408fba674a3aaca2454aba1ebb2"}], "guid": "bfdfe7dc352907fc980b868725387e98d27bdbc64f562c9b5d260ecb256e84ba", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989d0de0ab01276be0442fbf51f450e3e1", "guid": "bfdfe7dc352907fc980b868725387e98ef06182fbaafa18bc2f399f1c070f2c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98124c15a7d53b67aaf27a5bacec2a091c", "guid": "bfdfe7dc352907fc980b868725387e9821022b341ea79e7d3b280668e606b9de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f39e05b1edb726cec24695cd792eb6b", "guid": "bfdfe7dc352907fc980b868725387e9829c4079e4eef16e07de7a576219c986f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3637a4b1f5bb96129d5918529872d6b", "guid": "bfdfe7dc352907fc980b868725387e98e18437b48d94f199d7e0a5389c3fa4c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816077b2e815fbd12a6aa4a667bd729fc", "guid": "bfdfe7dc352907fc980b868725387e98f6f115a3778f23c122676400312ccaeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840522deeb77e672ff605a397eec706a6", "guid": "bfdfe7dc352907fc980b868725387e98c0320f6dcb1020ed19762833186ba4f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a479a4e00a3b9b8600cb934e542e320d", "guid": "bfdfe7dc352907fc980b868725387e980eab06ac96fdce51ab93a81daa7988a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98411a6b56952a1ff33f61cf68139da1a5", "guid": "bfdfe7dc352907fc980b868725387e9867f29a3f15c8de5b2379990e3ec34c3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806226ba42c26d0f8d2d2c352f1700abb", "guid": "bfdfe7dc352907fc980b868725387e98ef42abf10d0205b00665e97c959dcb08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874bdda06c1e205c3effc42d187dd1cc7", "guid": "bfdfe7dc352907fc980b868725387e985e47c9e9b0a8881bfabb4ffdb45a8605"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820f6705d98fa06f7cd7a3a4a42b23212", "guid": "bfdfe7dc352907fc980b868725387e981382d3455de88e5bfc010c722093e468"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860f380a79a457372c0a0fc2a7f2f64d4", "guid": "bfdfe7dc352907fc980b868725387e9869a2e58df8220562409047a328050bbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877fda99a889d18fbead628a91b2f9691", "guid": "bfdfe7dc352907fc980b868725387e98cb6a5b2c77cff2f64e8f0734d55af0d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f893ab421fa191663d32436b9392068", "guid": "bfdfe7dc352907fc980b868725387e98e7036ef1aac2c1992aa12313876c153c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832a72580a15174dab0bfa6b833a4f52b", "guid": "bfdfe7dc352907fc980b868725387e98569eeaa0861d59195df5395b10a365cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa1960a40ca8d87275e193c02dc09634", "guid": "bfdfe7dc352907fc980b868725387e98ded5bd9f8f69b111dac08a18787bfb1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f7bde4f045ea660dfd3d2a0cf789811", "guid": "bfdfe7dc352907fc980b868725387e98228d5dca5e4e1bbb14cb5d3ed6186665"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d0b9ad4068054332a9d3a6de24ca756", "guid": "bfdfe7dc352907fc980b868725387e98ad9795995292b99c63e1a93dcd8484d4"}], "guid": "bfdfe7dc352907fc980b868725387e98c4799bf7ac9c22d03d6cbd865ab49a71", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e9897276106e63db9ad12f29ae70c18063b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987baac6cb734d714618ceee4b0f8a57d6", "guid": "bfdfe7dc352907fc980b868725387e9805d7a2ad32499d86afc2404cdb3c9c52"}], "guid": "bfdfe7dc352907fc980b868725387e9851d26babbceaed774af060d75b10a536", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989c4b9f68929a14eaa10af2649d5a9a32", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e989a2136089a444bdd185358671df22960", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}