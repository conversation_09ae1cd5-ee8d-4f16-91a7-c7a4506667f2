{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9859342c0402bf70484b28447b874be184", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9857e73d19690f8b11dde6fc4e55961c02", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9857e73d19690f8b11dde6fc4e55961c02", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.29.3/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981584505fee6e9f29b1bae753642db588", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7dfc7b7e7e228d4b3a21ffddda0501c", "guid": "bfdfe7dc352907fc980b868725387e9843466c644ee31bfccd3f359ec35a7029", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e9c7b7436fd33f4c496e1a30de41fa0", "guid": "bfdfe7dc352907fc980b868725387e9824eb95ca06d46d0116bbdf44b1956e39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853e7749952d7aea034b4b4337317a82e", "guid": "bfdfe7dc352907fc980b868725387e9883d9bc4c142b0bd4d92c871f155fdd6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dbd2c5bca94202884214b7dc2ffb207", "guid": "bfdfe7dc352907fc980b868725387e988812712d0cb82df689c3aacef3bf71e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986263292dfb42c05d3b3fe9018a4c4001", "guid": "bfdfe7dc352907fc980b868725387e981cca3ef2d602091732777c25dcedc371", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fd5f998fce00a1319d53013a101fb05", "guid": "bfdfe7dc352907fc980b868725387e984cf5ab5361ae3681581d6fd9546a878e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ca1f92da067558c0304e74eb7b185a1", "guid": "bfdfe7dc352907fc980b868725387e98a9dd6144485ea58d5b3608fc6b430806", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98229f374debb2b7f586edf8328d55b9a0", "guid": "bfdfe7dc352907fc980b868725387e985bd755b6f261edd055236c5b2615b0ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8f0edc63132d7fa094b30ec2a2768f3", "guid": "bfdfe7dc352907fc980b868725387e98c7a1bbf35cb342057f2dce26a9ff9c9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bff4343f021d2a8318e279612bb0461", "guid": "bfdfe7dc352907fc980b868725387e9887f773bdf912054e545847e8cea9c772", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab1a429ac10e33b82d835d3066ce28de", "guid": "bfdfe7dc352907fc980b868725387e9826ef178a68ba267fd937286d9636f1e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982872bc63ca3ef026ef157d2a6de625a4", "guid": "bfdfe7dc352907fc980b868725387e987504609fb5db68e1039ed8b36b50379f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f076840671beb3919b42b70b3234115", "guid": "bfdfe7dc352907fc980b868725387e98e22017c171bb279f16c18938a3223358", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983eb911ba4b97174a383d21abadb48f7e", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989de902f4eaaba4bea99485fa69ee3146", "guid": "bfdfe7dc352907fc980b868725387e98fc6508d4c071055a2cca08eeb8f99790"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847514b4ae15b8427a16d846eda9fbfe0", "guid": "bfdfe7dc352907fc980b868725387e98c3592722b447a3e341639594038fd60f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98819b01d03dca5ac2079d3a1ecc9ed556", "guid": "bfdfe7dc352907fc980b868725387e98cda5926cba703fd79d2179046fd17d58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98622e06cae4c35c0033cda2ea3b1266d2", "guid": "bfdfe7dc352907fc980b868725387e981b4cac4c8232441eaaf8ae64818009cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4b4c4f698c36c0251966eabd800a59b", "guid": "bfdfe7dc352907fc980b868725387e98a1c8c58f23b408339fb6d75ee56da41d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aae2f7e98caabfe9bb1af867c15730a", "guid": "bfdfe7dc352907fc980b868725387e98e5239e0f7b7034138b7564e491c922e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2b7b47c256d17aae81b6391693cf728", "guid": "bfdfe7dc352907fc980b868725387e988ccdf41d18d55d5658e5d63d4669cccf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af1ada1565c9127acdc2e070045aaa31", "guid": "bfdfe7dc352907fc980b868725387e983112235bfbc646d676eece2c72ad4ef3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98629ea16a266a479bc19b75f60907831d", "guid": "bfdfe7dc352907fc980b868725387e98199b942a28d2b2254e3af655d5e99376"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffc81bf1451dc918ac9a63e2de2699e4", "guid": "bfdfe7dc352907fc980b868725387e98e22cf7470d321a3c49608b6a509fddeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d2c632aca42c1608e8b7797eba17bad", "guid": "bfdfe7dc352907fc980b868725387e988e4d60a90d1ee1b11372250fb1af5d78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6a27db4714b735aef56c6bfa72c292d", "guid": "bfdfe7dc352907fc980b868725387e98e4892f84fff491db0406e175242f976e"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}