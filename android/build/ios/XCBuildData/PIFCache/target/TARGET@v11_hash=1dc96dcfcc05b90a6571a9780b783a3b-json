{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a05a27b3e9ad287c4a6e33531ab5ac8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e5a3e91c3ffdccfda4e7b0bb6b571ce", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879296a82f267ca92e554cc0b8dffe4a9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98068749e03b075eef7aa0513b470b0093", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879296a82f267ca92e554cc0b8dffe4a9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982e804f72f773bdf4d5870a9df7590728", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983c0289c09e6b3fd9e5871d67502ee7e4", "guid": "bfdfe7dc352907fc980b868725387e9818e2495aea5d54113ce2b91b7f7ec362", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5ad233b4c5b230b3ad22bcb3d36ad33", "guid": "bfdfe7dc352907fc980b868725387e98ac4822ec3f4cf3aaf78fc3f55ad69af2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e46775310045e62c95fbdf4f52453f54", "guid": "bfdfe7dc352907fc980b868725387e984ec24afb450885b1da6df2fb2a670c24", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981221fe9ddd50c9ac8e32d1106c02aa0d", "guid": "bfdfe7dc352907fc980b868725387e985c42e291ac22052407e9adc07e6d4500", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9df9d66be24a79406debc2efc1e6be0", "guid": "bfdfe7dc352907fc980b868725387e98f7ad412abbf76b0855d1d0d07a40dcbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888e02cf3687f1df9925711090f37e1c5", "guid": "bfdfe7dc352907fc980b868725387e9870ab0dfe27a791e892d80d0e93977d84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881a27aa04e063ac566da5f8c7b527fe1", "guid": "bfdfe7dc352907fc980b868725387e983ceeaba8bd593c77fb5e10dbb65c38a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887cef3e82f32fd6fc1751a11f210df86", "guid": "bfdfe7dc352907fc980b868725387e98a0c4fd55047ba99dfa1186a45c31f2bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808ac2db3c86f991c10f442d1930349bc", "guid": "bfdfe7dc352907fc980b868725387e98fb4160d83da71a17e0df14232eca3ba8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea428e2a0c370b0b42f7b52e07010a58", "guid": "bfdfe7dc352907fc980b868725387e987e32aba304b551ed6312de4abfc6a077", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6900464de733c8d898afb0d37821504", "guid": "bfdfe7dc352907fc980b868725387e9816875425ed1b58133b8005a73e530ec6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cf663c73b4eaf23c253ac9b1fdfc56d", "guid": "bfdfe7dc352907fc980b868725387e984c70e7eba592bd399e24ea34bf002b48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869a0c45ea95ef0e401f246e028164c04", "guid": "bfdfe7dc352907fc980b868725387e981c067e94ec2b7e0e488caa9aa0414849", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1999b8ddad71a8c47d57db6700f2363", "guid": "bfdfe7dc352907fc980b868725387e9813e448aaa0591ccda483d834a3bb1067", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b3e3329f0a9571347ea1b9fc9d4236c", "guid": "bfdfe7dc352907fc980b868725387e98c5c9d39b56bcb63d127c00bfe7f7053c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98067eaa4885a348d5fe1f95c9946032a9", "guid": "bfdfe7dc352907fc980b868725387e9846b3ce35dc56c2c18eabbea2c82d52f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980062827fc3544af32c881cc097f7c921", "guid": "bfdfe7dc352907fc980b868725387e983d501f83313a610dd90aa5a97b6f7ba6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7816712885718d53f4d14bded5aa348", "guid": "bfdfe7dc352907fc980b868725387e98b4f432d1b056abb68fd0b03f192f96d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cd02b5c33d5c6a537c89529c761694a", "guid": "bfdfe7dc352907fc980b868725387e9850266e315c656a025de2be869fe1608e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98967a8c93e738118ea9d8ef55aa03f127", "guid": "bfdfe7dc352907fc980b868725387e980f706ac8bcd6d9f57ccf14eb3c57ee75", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876350b18e7bdc4c5d26f4b16d75b5d0c", "guid": "bfdfe7dc352907fc980b868725387e981125588dff555378a6040db58acc438b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb83dd903c5f2a936eb1d58cb6161cb4", "guid": "bfdfe7dc352907fc980b868725387e98b27c6cdb8f07accb6895d338611e4c25", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e77412d6331d7a4655a3014d52241234", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e8b71737fd73575f60477d0f3954f856", "guid": "bfdfe7dc352907fc980b868725387e9892ec6905a9c87049dfa7f271254b8e48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b258bcdb3a087c7b4d3f10bb20cc5bec", "guid": "bfdfe7dc352907fc980b868725387e98334c06f282d2f28d38a493bbdd04d4e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cb6aedaf281991b06655cd006a4a5db", "guid": "bfdfe7dc352907fc980b868725387e98940b057045cc435c8f7917f8f598e607"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad4888d4b20e9462651e7f36cfd0a337", "guid": "bfdfe7dc352907fc980b868725387e98c9aaaba5d8a7a402b526b98d054c40dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837d3ab185d99a7ec3baab65ee978a491", "guid": "bfdfe7dc352907fc980b868725387e9850c072423f3d0bcc1781c9a16bc44ee4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807c749c023452cbf33dfd2c1f7e56537", "guid": "bfdfe7dc352907fc980b868725387e987390da539238358e30b8b6e554a146ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5e33adee350703f2d5981ad3177cfe3", "guid": "bfdfe7dc352907fc980b868725387e98cd91d2a944e0b21d2ac5884e7d825845"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df83d29db933c639c2164d619f7dc2c8", "guid": "bfdfe7dc352907fc980b868725387e98a85d46a8f298e59b25d2f9469bf006cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd1da1eb2fac02f84f4fb3fb7beef35b", "guid": "bfdfe7dc352907fc980b868725387e9808392bae210adf005a3fba2236e2c82e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b71ef9e254fc6af5345b5ca9f5ec98a", "guid": "bfdfe7dc352907fc980b868725387e984f807404478b5ada54c5b5d23e5581fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872ce869f8d2a5064440e88f71cdcdb97", "guid": "bfdfe7dc352907fc980b868725387e98f18c9a546c3a9cb3a43acbf7308e4566"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983002f02f6d5c9c204ad19eb68d559bd3", "guid": "bfdfe7dc352907fc980b868725387e98e8b593e105436bf87dc091d8a2af0f3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983acbc5fd355094bf8516623392b3563c", "guid": "bfdfe7dc352907fc980b868725387e98b1298a5aa34ad3e1d0113b0061bff8e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b47958ba74e6aea256e9a5ccc724138e", "guid": "bfdfe7dc352907fc980b868725387e98fde0c5dcff3369a6130d66298cc5dd7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3d06d9995edb35ba3cf279d228416c9", "guid": "bfdfe7dc352907fc980b868725387e98706529ce9b9b416978de48067977bd55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd198b417a5783dd7226076b1b472308", "guid": "bfdfe7dc352907fc980b868725387e985643491d68e8255bdb36b84f52567723"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dde9a2e41d5a92ba5127d2d9fc2fc629", "guid": "bfdfe7dc352907fc980b868725387e98e5f922c58e6d861e9f975b849e6b895d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f6b7c7bdce6032a7dda0cb5bd9ae80f", "guid": "bfdfe7dc352907fc980b868725387e984d6702534a4825c0ac844ffe660d8ad0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a5ef187746f7eb7d6d94e76fb594b45", "guid": "bfdfe7dc352907fc980b868725387e98cc2091693822be0ecc41e58e91c7869e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d8fef4baa009ccf12d7d5ec2f9604c1", "guid": "bfdfe7dc352907fc980b868725387e981347e54e7174b5a377a179c1ec1ca3af"}], "guid": "bfdfe7dc352907fc980b868725387e9825719b484e0aed6b861e783d2f42a54c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e98b68373c4c7e8de8b4959804f141e12f6"}], "guid": "bfdfe7dc352907fc980b868725387e982c84369333b3374aebf87fe0162799c7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98310fee3616a59bb6bcc522a1464bafc1", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9848d65e592ffc6698e3a3af322a435b03", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}