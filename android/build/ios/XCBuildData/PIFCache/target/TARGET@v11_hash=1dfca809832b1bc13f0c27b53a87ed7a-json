{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980ca870371e013d34ea928666eab129f6", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981f07e23f361affaa82641996fa7c761e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3a9ccb9c986384045760ee758be7c1f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a3c131dfad10128b8bb97345b99d862b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3a9ccb9c986384045760ee758be7c1f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9871b51660889ce47d4d680b91c2ad4c3e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e7071c67f10596b5b67cd75db2ce5f0f", "guid": "bfdfe7dc352907fc980b868725387e98548a49e070eb4b617e993c6f735f7075", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888ba48b01a5c428e7172f7a0802050a2", "guid": "bfdfe7dc352907fc980b868725387e989508b6f6d65f051b639cd41ddf0223ec", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e980d165cea267b458bdfb8380702a57297", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c544db0a9dbfc9087ef4f535b5487fd9", "guid": "bfdfe7dc352907fc980b868725387e98e49bab2c2433ddb87065b31f0fb8e6b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a449af9b0750d753bcc7c67396aca29e", "guid": "bfdfe7dc352907fc980b868725387e98a7b83239db34f088a5822c6e5609bc91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb2910e15226915c7a27e0e37898622f", "guid": "bfdfe7dc352907fc980b868725387e98dbed14b404e0341c3e8351485356e205"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8f983cb2b4f21d5317fc8fdeb0ba39e", "guid": "bfdfe7dc352907fc980b868725387e987ec9336168533ddb89214779cee3bfdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9eb485ec6b5dc56cb25e55723c93b0d", "guid": "bfdfe7dc352907fc980b868725387e9890febaddcad240794e3da1156fd86657"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987919d96905ea0ca034c3e0575964d284", "guid": "bfdfe7dc352907fc980b868725387e982bc56dfe0bab9c61c3fbf22851fa80a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b89982311b1ac1bdae1379439d86d9a9", "guid": "bfdfe7dc352907fc980b868725387e98b735778a2fdf59dca8b7b5efe89873e4"}], "guid": "bfdfe7dc352907fc980b868725387e9889a8d736067114672d7d6ca955ac46df", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e983e72b155fa3cc96077655bab6b635c36"}], "guid": "bfdfe7dc352907fc980b868725387e98ec273f6bb2789845ac5d1a0c51d095a6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98da9966ad86bea35c87dcbd7bd9d1f9d0", "targetReference": "bfdfe7dc352907fc980b868725387e98f5cd644fc2aeb8654450a2168f52697c"}], "guid": "bfdfe7dc352907fc980b868725387e9884a69002072cb3a11d176db49db80273", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f5cd644fc2aeb8654450a2168f52697c", "name": "SwiftyGif-SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98290968646de0d07c6f6e2ed9e146ea78", "name": "SwiftyGif.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}