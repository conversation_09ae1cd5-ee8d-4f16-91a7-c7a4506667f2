{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98982900936b7136e5ab50c36c8d7da360", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFunctions", "PRODUCT_NAME": "FirebaseFunctions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9874ee8fc64976e27a77f32496e83d0373", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ce15f264fab92cc3c0886c546a1057aa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFunctions", "PRODUCT_NAME": "FirebaseFunctions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98405f07cee1bba8f055969423bfc36d82", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ce15f264fab92cc3c0886c546a1057aa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFunctions", "PRODUCT_NAME": "FirebaseFunctions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987d06578b531da9962c2bde2529be14b8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d6a7624df98fc0e9a93c9fcfe4ec2ab3", "guid": "bfdfe7dc352907fc980b868725387e989159fa24014cf8c4b266364d925c00a9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989ecdd030d49c6a24a6dd852d042b3441", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ed02ccc261206b6c0fb8863e9b7ba5ca", "guid": "bfdfe7dc352907fc980b868725387e98d2c9ef08ede926b0d4451d8c16b9c963"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982173a303afce60fc6d416239470a610a", "guid": "bfdfe7dc352907fc980b868725387e98727417fbfef2a7ea58e609b591477805"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e4b29be8e067402ac7c86dc3c53792d", "guid": "bfdfe7dc352907fc980b868725387e987265aca74d2e57e9f9424d364174a17a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98914ad674c5b4af2b422892c558a1e9ac", "guid": "bfdfe7dc352907fc980b868725387e986db230c0d45d679be13d476e84b12de2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b21e2b1bd4c1af636fcaeb833507dfd3", "guid": "bfdfe7dc352907fc980b868725387e98157c1ebcb6df151bd20a16a550130328"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef4ea59a843a8df251aed1aba30440b3", "guid": "bfdfe7dc352907fc980b868725387e989acfe68dec380e4d480a61698ad8d5a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828f9faf27cfd1f2806f6611d7272dc3f", "guid": "bfdfe7dc352907fc980b868725387e9824ac2ec2aef28b067dc3a686ce46eb05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982299da7dfadcf97ea98c8b290bbae86f", "guid": "bfdfe7dc352907fc980b868725387e98be20509f9fd556b0ed4ef2746beec81c"}], "guid": "bfdfe7dc352907fc980b868725387e98882ef1d0e1e70240d42f0c97ece7caba", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e98aa5bcc11dddb408d81532bdcf941fcb7"}], "guid": "bfdfe7dc352907fc980b868725387e983cacff366fa736644fe32c8ca8bfd402", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b0d466f5b1a145e84f201547ade8cfb3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9863397a73468de8eb61b23cd9f8f91a11", "name": "FirebaseMessagingInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}], "guid": "bfdfe7dc352907fc980b868725387e98141d7db37e1ba5fe325a4a19cf71e38c", "name": "FirebaseFunctions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98422c2b3f76d0bdf719933c892e25e1fa", "name": "FirebaseFunctions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}