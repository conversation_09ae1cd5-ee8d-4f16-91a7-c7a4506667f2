{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a435ef741a3680d479447117711a8195", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dabc22f3bc9d8d8f3a240d2d5a0d9ec6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9814f20131c91cd4c4ecc363d13680c55f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98085bf4d7488ddd242d1451f4db3f6ced", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9814f20131c91cd4c4ecc363d13680c55f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988eb90cc5c44fc13b50f0e7c4403ee20a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b837952f011dd4dc99fde4c5b9daf18c", "guid": "bfdfe7dc352907fc980b868725387e9807d828c79211c91b36c82e323e543bfc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98205ee0ef929fd01510ffde8c76d9b78e", "guid": "bfdfe7dc352907fc980b868725387e984339d74fa149f9f0950cdddcf98269b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1a7379f44d8d30aa22b9c6ee8b68786", "guid": "bfdfe7dc352907fc980b868725387e98d04a02d6e1d5ade5875921427d812850", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c7cd747b49d25e4eae0cad1d486c560", "guid": "bfdfe7dc352907fc980b868725387e981b6898b25a0ec62b7d1f8d4626f3eee6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98305e049ae10b3773841d08e6346c9659", "guid": "bfdfe7dc352907fc980b868725387e9829437978b9fef43621810e7ebacfeb0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98254c8e9863b3be9fc98dafef364e91ca", "guid": "bfdfe7dc352907fc980b868725387e98eeaaf8e1bffbedf9c814c7a08c421592", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98868df69100483d813d4d9b6a188db736", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980b6fd75eb0550aeb2b335a89857675ce", "guid": "bfdfe7dc352907fc980b868725387e980339d7a6443bbc589fc647b08dea8d81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe15423396873b548444d21b9a0fcaca", "guid": "bfdfe7dc352907fc980b868725387e98bef0b13445ee3ec37502c4e8a0ea835b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860f2d71661114d2af2a91ccf02f51e02", "guid": "bfdfe7dc352907fc980b868725387e9859ff7d1f8db8451828841a99ced16f59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98155f5f5cfdf036ec8389664b989b9365", "guid": "bfdfe7dc352907fc980b868725387e98d4645c396db1e6d631eda4a4ea7af0b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ec23e2dd2d7e761c629a7f4abba180f", "guid": "bfdfe7dc352907fc980b868725387e98c4a9b55f372986f20d394587bec3721c"}], "guid": "bfdfe7dc352907fc980b868725387e987bd85f369aa694cfdbd302fec63e1194", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e98f8162417d30758283623ae0eb56c3d94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987baac6cb734d714618ceee4b0f8a57d6", "guid": "bfdfe7dc352907fc980b868725387e98754aeb814bb1efe9d05243d84ec0e065"}], "guid": "bfdfe7dc352907fc980b868725387e983c47bdb1a185237b0c23845e63bf25ba", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b6503efaa264a398ae7305dfdf6b6eff", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e981b3d5d7ad510d9fdc8859fc2208c3b62", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}