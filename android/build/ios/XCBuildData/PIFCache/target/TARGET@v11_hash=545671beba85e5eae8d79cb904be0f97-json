{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984ca43225b378ef1b839582cd9a15907d", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9827b212aaed4c14c2fff36b0cc033943f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989ecb66d9136e20a25f4f14b89428a104", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e984737774ade080ae4dab2430a95a4140c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989ecb66d9136e20a25f4f14b89428a104", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9820482013cd7662ff5e6f9dd179dbed62", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ac2f7021f72da5a57d4730212a42acc5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e980224b3ed7ad8be182d2b4e3cd06dfa5e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d1ba67ed9aabd9e1ad88895ace8733f2", "guid": "bfdfe7dc352907fc980b868725387e982185069827d94bc63a8e7df8e8909428"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a58eec5ebe1f7ea72df5a82f5c1e0a67", "guid": "bfdfe7dc352907fc980b868725387e98c5becb444729c79c6eab4e9bf86d6b8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804ada3bf381616330d30b81b55cfe540", "guid": "bfdfe7dc352907fc980b868725387e98dadca543d709b199657cf5a6959a279a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9ab890dec6535a80b5479f05d94d8fb", "guid": "bfdfe7dc352907fc980b868725387e98997ddbb653262e2f6b3c119cb775d3bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895f06aee513837731cb729b44756a57c", "guid": "bfdfe7dc352907fc980b868725387e98c5cc941a46dc1785f354a626bc0a2353"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dceee827e643510eef65217a265a50f5", "guid": "bfdfe7dc352907fc980b868725387e987144d5e6a0497d3c3bb6f074575ed260"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866ba847ed3b1d60fc1013f23618468e4", "guid": "bfdfe7dc352907fc980b868725387e989a74fee32d76419b48e30f4d58970bb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d950dbb6b72bdfe7f3885ef39d531eb0", "guid": "bfdfe7dc352907fc980b868725387e98e2b3e5fdbfc647dd7a50183a980ffa2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98458797f1e93dbd8125fd5cc25b5d4915", "guid": "bfdfe7dc352907fc980b868725387e98c66be4f0a92614fd43fe8bd3bbcf6f1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba9f644744f369b5803164b2f38f55f9", "guid": "bfdfe7dc352907fc980b868725387e982e963296bcd70b71720b20f583219202"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b60dbd29dc569491c9abec072e3a6ca", "guid": "bfdfe7dc352907fc980b868725387e98528398e8324511d784cde134577a4b04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884920e850ae4235d5d2f4d62da92c8b3", "guid": "bfdfe7dc352907fc980b868725387e98722c4941dedcedfcea08d95cbe2127af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b239af136bb0f475f4b66037091ac70b", "guid": "bfdfe7dc352907fc980b868725387e980a11a8a1a2adc5879d466c8f049438b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857945abd22867666e1c469ce26e31945", "guid": "bfdfe7dc352907fc980b868725387e983077535f552a22ebff2440256efc6e44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a563a23088788fd8fda015c6a9d145f", "guid": "bfdfe7dc352907fc980b868725387e985221cd96b8d4bca5d892e7abe614de7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98995139bed2915b150f37512e839d6109", "guid": "bfdfe7dc352907fc980b868725387e98d81dd31ef85352bf3c025f0554116222"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98786b89400a3742d7c11369b947da230e", "guid": "bfdfe7dc352907fc980b868725387e98213d4917175a98f3de63745e75634eac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd6014846392c0eabc8f24f602c1422b", "guid": "bfdfe7dc352907fc980b868725387e98bab9bb91504a3ed0d95cad9c1b7b5c56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870664f3d8297ba8cb22f0b5cd71fc7cf", "guid": "bfdfe7dc352907fc980b868725387e987e6c9a009c25f64bd667fbd6134c61a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ec79a608371d6d0e9b53368d90308b1", "guid": "bfdfe7dc352907fc980b868725387e98e2ddacc377d01a17e1f8a13610ea08b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f69cd1aa81d9426bb3c54ac5ad2022bc", "guid": "bfdfe7dc352907fc980b868725387e98eb8b6208853b124944a4c1a6600e9a11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3ec9440c73c88779ca1157f988f2b8d", "guid": "bfdfe7dc352907fc980b868725387e988853c970cd413f6e5891440bd7997568"}], "guid": "bfdfe7dc352907fc980b868725387e98dcdd917d7d3fcb10ffe86583edea02c8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}