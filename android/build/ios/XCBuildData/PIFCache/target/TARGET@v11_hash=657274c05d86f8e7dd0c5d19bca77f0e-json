{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988b03fcad054e6edf192e2c7df0bdb6d5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c7943a3663fffe8481beb31a7328caf3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9806faace0700472b26f3ed11906650ac9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e9a2592e6229d75a1da535da9950d793", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9806faace0700472b26f3ed11906650ac9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d7973f80dc397a8f375ee36058ebd57c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985266cd072b3f30ffece3f4001f83836e", "guid": "bfdfe7dc352907fc980b868725387e988f985708a03168f91c9c75ac321bca19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac0dc5eef73a9c27c55a13b5c1d0f87", "guid": "bfdfe7dc352907fc980b868725387e98a67eee4f7908306a1d22a6d554fb5218"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c65dbdbe05a71e2a1605c3156175a82", "guid": "bfdfe7dc352907fc980b868725387e9889d394e72abc444e838a89ac25af78a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981324f736d9587e343f1f0b4b4168d519", "guid": "bfdfe7dc352907fc980b868725387e98f05d184e65c4301a4d2b4c9f3202029c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ad8335155876277f8f169ae9b0792d1", "guid": "bfdfe7dc352907fc980b868725387e98323f622004a5f6801d007ad789cd0d85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987af2387eb1d56456b446a778ec750c2e", "guid": "bfdfe7dc352907fc980b868725387e98da89f709639002bd974c6c24fb13e211"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987db9ec50ca157b2b704aee89fef94816", "guid": "bfdfe7dc352907fc980b868725387e98528b001de0c5a6e724d1d78e5f914bec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8976f491399a975590b978106220aa9", "guid": "bfdfe7dc352907fc980b868725387e98c8e8dbdfbf00e50be3ed57da4a9b3cb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818b50efee736e7cef979deb23b431080", "guid": "bfdfe7dc352907fc980b868725387e988bb3764a295008224a29a2e694ed9ab5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4dce8911c6e6a4e1bf6df59a96d0c3d", "guid": "bfdfe7dc352907fc980b868725387e98c733c3acf9e70717ffc67b6084cb1c9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8cb1b4723fe0cd9c7af5d4a2eeb1683", "guid": "bfdfe7dc352907fc980b868725387e98a5c9343f136ef13dbca5f74571b97780", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf0d0de8f0497f664281d29bdb3b5be7", "guid": "bfdfe7dc352907fc980b868725387e98475d30e15c1c08116451b57ff6add8a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f47b8b61ff7706318bce3c651ed1d0c7", "guid": "bfdfe7dc352907fc980b868725387e981d41d7a8cd92c71b48a53a513ae80207"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b83b3cdaee89a37b69d5b460274df09a", "guid": "bfdfe7dc352907fc980b868725387e98c7b0b84315d61884c76ce7eb4975a7d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7089ada24f2969b918ee0e106de79fb", "guid": "bfdfe7dc352907fc980b868725387e984aa05cc316d896632b7d7d8bf6700e7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98897f04244ec00e8c92b6001f596de3bd", "guid": "bfdfe7dc352907fc980b868725387e98bdec9609e4cf51c3d7b11d582b9fd005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ec70b258b69175c93f22b35f70c8dd8", "guid": "bfdfe7dc352907fc980b868725387e98301d407e03c89c98442c17e9c8c4503a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b27b10a00726a05f1307dbfad66bdab3", "guid": "bfdfe7dc352907fc980b868725387e98cd5e68371e4c900143dfe4bc51061763", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6c18fced585b006ae00731d02226e0a", "guid": "bfdfe7dc352907fc980b868725387e985629b798416e2dd79f70997733b23f72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac62605739a1f7096618ca6d289c39f7", "guid": "bfdfe7dc352907fc980b868725387e98e9121cb9a43579c529f09505e93a6778"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989930570201ccde5067a13b6ae42cd722", "guid": "bfdfe7dc352907fc980b868725387e9877dec3c5e6d43019226730744e5f7957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859ad201e3c42e97a5998e856561ab982", "guid": "bfdfe7dc352907fc980b868725387e98c194fa7f39f749fdb87d5cdd9cfb09f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f24cc5685122210bc21583d1840ace52", "guid": "bfdfe7dc352907fc980b868725387e985794ce27701d84d2b88906533b4a28d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f15bfcc534ce00f659fb087c09102e9d", "guid": "bfdfe7dc352907fc980b868725387e98ff615986184141f1b21d32b62fdb8467"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895d40a674dc20e5b054aac7a8df4e86f", "guid": "bfdfe7dc352907fc980b868725387e98527e3121482061c1198a88f482df2caa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2dc2153760139517156904be6863143", "guid": "bfdfe7dc352907fc980b868725387e985ed379f820b4a207a4debce4f21f539f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c37e3ad1442977b5d31d4c5cfe3e2e1f", "guid": "bfdfe7dc352907fc980b868725387e986571d29a93d5e062f7666d5df74fbdee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98869b3f225d56a809eb438234c17a7d8b", "guid": "bfdfe7dc352907fc980b868725387e98c6080fda33ce2d3d7272ed54a5b1c937"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a4620dd2edab414623983ef05478e08", "guid": "bfdfe7dc352907fc980b868725387e98d0fde4f02e7a2c60b1ac92265de8eb0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f945dd9ccab2e2a62e7ac9657534357", "guid": "bfdfe7dc352907fc980b868725387e98f705d00420297c89e06352ca9dc37900"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcd1160693eadec05427f524f96f395f", "guid": "bfdfe7dc352907fc980b868725387e982355cb90932fc072488d38068aaab96d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836b3a5a4684286f1975dc8d1f631bfb3", "guid": "bfdfe7dc352907fc980b868725387e983658e29951a7b08593777d66c2d4b2fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f82bbc7082192cb70121687d30851059", "guid": "bfdfe7dc352907fc980b868725387e982464a17171c915f223850f9f4849c3ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fd16340c34d26d11605ee63f2c525c7", "guid": "bfdfe7dc352907fc980b868725387e98680ddf97b84b5d7936be3d55b87516e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846248215e175e5ed3d19d22d2d410f61", "guid": "bfdfe7dc352907fc980b868725387e98ec1c2b30eb6e71d41cccd8776368cf80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811ca073f84cd87c74ef09b8c8b6437b9", "guid": "bfdfe7dc352907fc980b868725387e9880e397e026bb9108d34d24e679647474"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b97f41d13991446a347fc0bb0240746", "guid": "bfdfe7dc352907fc980b868725387e981aeb2cbaae70f372da287763ece5404a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f01c3e93b9a35c5dfd949d6459d739b", "guid": "bfdfe7dc352907fc980b868725387e988f34e1a3d6915d6b80213d2a85efa805"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c386c951951ddeca166f8eb4cec7c23b", "guid": "bfdfe7dc352907fc980b868725387e98735931593a18b15fb6ea1b44a28d2747"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98632162553198aca5df921a01d756a04b", "guid": "bfdfe7dc352907fc980b868725387e982d655a46e01330f4edec59a0726e154b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8d09748f7d5605052a9b3c7b0b0633c", "guid": "bfdfe7dc352907fc980b868725387e98e54a1a58293667b7debbf365edb19b6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806472a429f2629a4cd187f3b5bf7aa45", "guid": "bfdfe7dc352907fc980b868725387e98fc12759298e08e8e581095430fe74521"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7948b1101bbc5280e1626921675997d", "guid": "bfdfe7dc352907fc980b868725387e981ff6dc067a15bed72487035e9b2fd27d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899c13e716a9d9550960a571ece50f156", "guid": "bfdfe7dc352907fc980b868725387e987626aefc28216dfe15c70a899c03c7a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a622146c0cb4bb31ffea4a25f0c7cf0d", "guid": "bfdfe7dc352907fc980b868725387e98b39a22bb4f2781e843719c34a2c810e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c5d53d2ff2f2521ebc678390f39c5da", "guid": "bfdfe7dc352907fc980b868725387e98036b6199663c24b0a44ce6d8fb75758a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819dc86d31494725b0467bfefcccb7ee8", "guid": "bfdfe7dc352907fc980b868725387e98dd49e4cb37dcad2749b7ea95458dd0db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880f75174499e2734e2fab02c9e70f119", "guid": "bfdfe7dc352907fc980b868725387e983aeb1ab771df3a98b0baaca4a15e680d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884096b9f0df5985d9f4b5ba6f351c68e", "guid": "bfdfe7dc352907fc980b868725387e98694a723f9974d8229480237e88b43f4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809ab45c2e27da25aadf6b1cf48d5f3ca", "guid": "bfdfe7dc352907fc980b868725387e9866dc2a097036e068654f23acbab504f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983929a90f442573a7b0c1da954fdc0be7", "guid": "bfdfe7dc352907fc980b868725387e98fd6f562a896354833dadee4f4272a897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9f44ba725b0cb2770c15ce1d3379b66", "guid": "bfdfe7dc352907fc980b868725387e981ffe238d91d845f4b5521f77221c624b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd198ce2abcb978c73a69a5fe87dc041", "guid": "bfdfe7dc352907fc980b868725387e9842d26bdc44efefb6785ca7988d959ad9"}], "guid": "bfdfe7dc352907fc980b868725387e983eb857b1cb22ed3df4d883f0b324a509", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98188e6a5fca3832e71ae54806cc9e688c", "guid": "bfdfe7dc352907fc980b868725387e98d52a3670da5feaeb5b5c1d7de71a6843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a638f471c79a38804e350b8529836f", "guid": "bfdfe7dc352907fc980b868725387e9899cba0c259cfa16360425d4015afc79b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984629ac010194987922ec959fbb8ea67a", "guid": "bfdfe7dc352907fc980b868725387e98667d3b05d61e30e7af8d5c03b95fd7ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877f695a88ca51be03e18041d7eb991ba", "guid": "bfdfe7dc352907fc980b868725387e989b5c083a6be30e651b62154faeadaee4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814e178feeb9555ecb2a9da2b134967b6", "guid": "bfdfe7dc352907fc980b868725387e98ba28dfb654e60505e9bdf8c60617baa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ad8a65ea5d2c953272ae9cc3de871ab", "guid": "bfdfe7dc352907fc980b868725387e98c0758525f5fd2bedd846dd025419e1f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853ed4284e5f8089b127e13ffbf15c632", "guid": "bfdfe7dc352907fc980b868725387e98042a93a3e6d7ac4d7f997e7782c9eb43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989558cdb36ec4010435a688ebc32fa14e", "guid": "bfdfe7dc352907fc980b868725387e98f34684f623849f96664f36ef9d2e4da2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816b9dc2e95139ce48361427a3c5f18ad", "guid": "bfdfe7dc352907fc980b868725387e98479d3d9e86c25d82823d663c0b3b311c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a94466edec5d96782bec04751c19d02a", "guid": "bfdfe7dc352907fc980b868725387e98519d7e3bef761a274c70a1e40b58ce3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c313525b7d21bfccb71fa739ce3aaaf", "guid": "bfdfe7dc352907fc980b868725387e9804a7bb674471e3ac9d6ef6bb67bdfe3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e60da848be5d3c095d7416c9bb26cb82", "guid": "bfdfe7dc352907fc980b868725387e98fe9f6a938af6af54dee528d3e9ab3699"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d4bd67604fdbd515e727938cdaee540", "guid": "bfdfe7dc352907fc980b868725387e987a61bf984a0b7c3bc23d9cf083a38efb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a42d040112c0356ba9a22be682419405", "guid": "bfdfe7dc352907fc980b868725387e98d4138217828c339718c4a6a2de8f285f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98520578dd072099a9bcc2990be6a47ee2", "guid": "bfdfe7dc352907fc980b868725387e98369bbb1d84e6683ccd8f0366057ee575"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a852a378d70b4a99cc4fe9fadd2c4d8", "guid": "bfdfe7dc352907fc980b868725387e989b82cd607a79bc0e1211e05ddd270b53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837c1cc7ed9338828014e336b72b4d852", "guid": "bfdfe7dc352907fc980b868725387e9837ff7160e8507789b18388f20fa260da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf30225586046c9c7e0418919d9b0027", "guid": "bfdfe7dc352907fc980b868725387e98ac88946490df94e258f350b86713f48c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981287610e3360fbbec68f46323090f685", "guid": "bfdfe7dc352907fc980b868725387e982da860e06f20f8cd8e9c3f541b37fa52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b87b2db8c37876e814190495dde7473c", "guid": "bfdfe7dc352907fc980b868725387e98fcb866ecc999d6ad1a3a75c9cc8adeb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98814b8af33375c9a4f50a2ee98c483668", "guid": "bfdfe7dc352907fc980b868725387e985355f2ed26d47849b391054f77c43d1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d5ebbae46080cccfc6b4c710e8b9385", "guid": "bfdfe7dc352907fc980b868725387e98ae7a43dfaf38e408cf1be28170bff631"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983765d8cf3f338f3ddb8682b5d15c57e8", "guid": "bfdfe7dc352907fc980b868725387e987c558163846fffca6a6d3fec013e9af5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c75b1dcbc1697a982e6a80a9d9bc6ba6", "guid": "bfdfe7dc352907fc980b868725387e98619747f051e2020b61223009678da6e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab8bb448cf5781b50f6b40f099964e54", "guid": "bfdfe7dc352907fc980b868725387e9842b0652a458b2f792f41b1e5f3088489"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98653d3805622c4fda7a6e0e45fc647750", "guid": "bfdfe7dc352907fc980b868725387e986143fff938b00ed8c297fabbada1214d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f271057fb7fcdbfa82b80af92e90d1c", "guid": "bfdfe7dc352907fc980b868725387e98c4efddfae22105d92240ebae35a92a06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982617d736ca3c0c5f870db81323465954", "guid": "bfdfe7dc352907fc980b868725387e983f99337b776853b249293bb6754f09c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b67fe94a1982b05dd458e61bc6752f1", "guid": "bfdfe7dc352907fc980b868725387e98f979bf5e12a9e72cc9c3726484cb975b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a19e33271108788149cfe0d7cb3968b", "guid": "bfdfe7dc352907fc980b868725387e9861a390e317c40778336795afbf9f6ae2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c3c5282d4f1953a7a44a414d9aa69c6", "guid": "bfdfe7dc352907fc980b868725387e982f8d9eb210716c9aee218b11f8c66f10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98335aee10ef153cc41076ea627018dd85", "guid": "bfdfe7dc352907fc980b868725387e982eeeb4eab5a9873df292c698a3fd5f23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c0020d84379594d43ac393887e71093", "guid": "bfdfe7dc352907fc980b868725387e989af245a5b8b714794febffc9576433d0"}], "guid": "bfdfe7dc352907fc980b868725387e98daf04aaeec56169b8d92f1155558445e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e98c451cf37476dbee9e23d4bd9e94eb845"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807865304d85b81a8b4fcd5845f28582c", "guid": "bfdfe7dc352907fc980b868725387e985e0df711efbffb212756ba33ec1b1399"}], "guid": "bfdfe7dc352907fc980b868725387e9843c3e4ffea38313ba0f9166dec1413f7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98696ee51f0ab8dab17724587d43f7e1a5", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e989037122ebfeb2a031a65d39d1fa626d0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}