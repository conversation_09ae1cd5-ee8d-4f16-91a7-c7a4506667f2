{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7635ee285af22e0347e1ce0b4a90cfc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989932c4754e280d081528231a2670bc3c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989afcac913fdda3682a0098808488ec7f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984bf11ebfafc03b691bf835117e604040", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989afcac913fdda3682a0098808488ec7f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec37df74f11f28f53595b72bd78f7254", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c7dd4b186dde930c138a9a7a5f2d60ee", "guid": "bfdfe7dc352907fc980b868725387e9842aa39deb8d2eaf4038e55d0291c306b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988c02b53eb436bb69632712675b405ccb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98822551e02c6239b08455bd5359c91dda", "guid": "bfdfe7dc352907fc980b868725387e9872bb608d7b563113f6fbaa9d6e13f912"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98865fa817cddd74d2e381226375657fe7", "guid": "bfdfe7dc352907fc980b868725387e9861916069f00818840ee2e91f212c746e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f16948983f1f2414df942c9d93f6ed44", "guid": "bfdfe7dc352907fc980b868725387e9812f3259776aeee0ee7d5bf7dff6d5fb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9ea73b983a0c824e5bb92a19f293868", "guid": "bfdfe7dc352907fc980b868725387e9841fef1958dcca6858b869018f1f7ed6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b1ebdc171acfee900756b7f2c18d256", "guid": "bfdfe7dc352907fc980b868725387e98a537c1a8cf3d3bcea60765c5be448860"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878453185fa06abac4bfa2948dcfa5643", "guid": "bfdfe7dc352907fc980b868725387e98ef8c7223efef2119a7ca9c033446c8c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818edc86ad2a627933b83a50cba3a3843", "guid": "bfdfe7dc352907fc980b868725387e98655f6965c0100b1ae7f0318d5f3d4996"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9415ca6b2ee4c2e8aa8ccfec0a7f6cc", "guid": "bfdfe7dc352907fc980b868725387e98f548ffadf73905e239e755442e0d2a4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a372005af4d7c68a3df820e01e3cb4a0", "guid": "bfdfe7dc352907fc980b868725387e98a821b5370363a165dc24f3b0b04800e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9cd7d02a3f12b5eb26f8486da670c8f", "guid": "bfdfe7dc352907fc980b868725387e981c567d04d2db71dde16183ab4a18e562"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1c3ac1eda3b976e2aff771fb260a56", "guid": "bfdfe7dc352907fc980b868725387e98c47531ec7f15a2fed82bf4c9d69ee820"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98873ec0417fa883dccb9ce8da6db47300", "guid": "bfdfe7dc352907fc980b868725387e9879ffd5abbc3fd80d721ed985d61643b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c98b1527b58748f21bddf33df9eee92", "guid": "bfdfe7dc352907fc980b868725387e9802c324b89b737ab0f76818d53cac6124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983280406b2600d7147d4281a78c9be4b1", "guid": "bfdfe7dc352907fc980b868725387e986d429bfbd68bd7d360892107bd39eefc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98145e41fa42d2bc3d5e5a5eea9a99dcf2", "guid": "bfdfe7dc352907fc980b868725387e9866c1deb9d9a8ba1f54f2ae2785fb7e1f"}], "guid": "bfdfe7dc352907fc980b868725387e98827dd3d94b4414bd4796f5aaa7e9a314", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e98124f897e3ba540c2c7bf30ec1568f2de"}], "guid": "bfdfe7dc352907fc980b868725387e9854a12f82bb17c09a7e885ab33892723d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c8019ac644ac18ce42f0a00de40738d3", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98a244a794b06b8eea0e487fc3ef0e2a8f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}