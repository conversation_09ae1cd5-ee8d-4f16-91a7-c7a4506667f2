{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98175132a86c30d81269d495fc22229258", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981bd0a7229fb0fc75fbaa960252217cd4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9831f64714f75719721b9d05d37efa5a81", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9830f9ccada80bdc9abb7b7027ffb57c55", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9831f64714f75719721b9d05d37efa5a81", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984bcd6576290880266d6cc9714c19ab59", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98adfab015db2640a700cd57174fa3c8d1", "guid": "bfdfe7dc352907fc980b868725387e987054108bcefa0183202c3f87868a50e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3b462d73f749babe43a8b00373a0645", "guid": "bfdfe7dc352907fc980b868725387e98e85afc6884b0e999340c18b10298848f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98844b4170ca7ce527aeac62714345bf8e", "guid": "bfdfe7dc352907fc980b868725387e98899fce7ce9db1514075cf449684052bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e755b6d30e30dd1456a43ea26fd04e6", "guid": "bfdfe7dc352907fc980b868725387e983198f7b31e5318353f46a42f99a9665f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98642b94c16b80b9271ccb954b3217d87c", "guid": "bfdfe7dc352907fc980b868725387e98476c45da5b7e7e8281891cf36923db94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3ff6e25d603153d14aa9a592afef0fe", "guid": "bfdfe7dc352907fc980b868725387e98c359fbd2a0d41824a5cecbfcbf62e3b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811a8f805ffa79b125f3ce64e0915f4ee", "guid": "bfdfe7dc352907fc980b868725387e98470c6516ceabc31eb2d432cfc5de874a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3f76e64bbd8732b3e1ceab900a27899", "guid": "bfdfe7dc352907fc980b868725387e9886afa62b35a6bef9ec606363bdf70298", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892e4c1baed692acb34f13956fc78d496", "guid": "bfdfe7dc352907fc980b868725387e98b696415f189c07c56c85ce8881e62b75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d7d7eb54170002f8057582a1c9d00bf", "guid": "bfdfe7dc352907fc980b868725387e98e7f46d758befd1f174f325c2719492f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980042723f7b3399bcf14b9f13e04ac21b", "guid": "bfdfe7dc352907fc980b868725387e986d6d07ba25a8dc1fbb5932d57dd4409f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b1acccdcf75a58648d872a577a0ee12", "guid": "bfdfe7dc352907fc980b868725387e98e028318557efa414c22fb56b21321b89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983363533ad75e70cc3c652eef41548c58", "guid": "bfdfe7dc352907fc980b868725387e98af575e7cbfb5fa9fbe207e716b8b715e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac4a83ae940c8edca9746fcdc535ce03", "guid": "bfdfe7dc352907fc980b868725387e9823ff5bef88ab3ddc435472bf17be1cb4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbbb62f9b4587e45e64f606b7e559861", "guid": "bfdfe7dc352907fc980b868725387e98ada458343c2a7da6e922af281bb6a5f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98234f8379be96f7c042be1c3e9ec725f6", "guid": "bfdfe7dc352907fc980b868725387e98fdce9c13361d757688711077d40b9949", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e012ed87a907e5282bb9f164756ae12", "guid": "bfdfe7dc352907fc980b868725387e981b54fba5b87aa479a5b22b45eba68f9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98217d318eca2659203e7abc32b9c82fd3", "guid": "bfdfe7dc352907fc980b868725387e98196e0064a842361cc702ab546ad82657", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98427f95eeeb48391bd5b14f2d71a38dbf", "guid": "bfdfe7dc352907fc980b868725387e980e5bcbb48130de86ab87cf1327e1d858", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c43254e7636a7a8d523d8dc339c2a3bc", "guid": "bfdfe7dc352907fc980b868725387e980c199f3bb546dce5d2ee5c5318d98201", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce9069e6c80ceb0751d2d1b9d5bc538b", "guid": "bfdfe7dc352907fc980b868725387e985c3df008adc5eeaac8a1265a5183edcf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d79e29094772e50f6c920e626cd47b18", "guid": "bfdfe7dc352907fc980b868725387e9841da637fb91272b43f91cf00ce3e28e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98331f2196bcedb55a51f9982eb7a5de78", "guid": "bfdfe7dc352907fc980b868725387e9882428c27cdd1f92c60be694c246d148d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb67716a4ab0478b30fc0855cdbacb0e", "guid": "bfdfe7dc352907fc980b868725387e98074a5bd196123e6e36b4170f89888f4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f96ee367d47202cc9a27c96af72073d", "guid": "bfdfe7dc352907fc980b868725387e98a45ae97e920321a966687a5723412493", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98044764b3cabae3ba554a01a3000f56f4", "guid": "bfdfe7dc352907fc980b868725387e98a6f18fe7c086860d4437adf50da4b7ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a153f93be1cad6b7674076f9c58db902", "guid": "bfdfe7dc352907fc980b868725387e98c5dec93649888035a182211ec97af8e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980abc8c6de43d6740a2d87f6d66704319", "guid": "bfdfe7dc352907fc980b868725387e981ee9a66d0d8fc3e11c57bdfd039b0599", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d18ac592a0600c729be01ea70d143488", "guid": "bfdfe7dc352907fc980b868725387e9856116394f1198b89536cea8c64c1fec1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871334b779ebf82eab890d4c0d4cb8526", "guid": "bfdfe7dc352907fc980b868725387e988b117bf842346414fedae2b3a71777ff", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e980314c1befd35ea200b92b0911767b8b3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c6a76fbf92b93b3af48f0c3d55d32c0d", "guid": "bfdfe7dc352907fc980b868725387e98599e1e2ab44cec49c8593c6aa7c588bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989085c52fdaae9b32231e091d65fcb44b", "guid": "bfdfe7dc352907fc980b868725387e98b3516e6ad425853f3bea88beb1a481d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e155e98e14d8cad7dfbfc924886bed8b", "guid": "bfdfe7dc352907fc980b868725387e986d63335102b34f5a4e461e239102f31c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989135aa80f410f325cee04eadb3f6191f", "guid": "bfdfe7dc352907fc980b868725387e98addfe353b78597f89423a255e5d72178"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e13c93004f219f28d9492782cf2e4dd", "guid": "bfdfe7dc352907fc980b868725387e9877da74e2526f554c4e61bf87afb8a364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2216470009ad2d10d7988b6ebc4907a", "guid": "bfdfe7dc352907fc980b868725387e98f854c6003c076e3b067dd0ee9018722e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833cdc1581a320f3fe0af3af901f3ad86", "guid": "bfdfe7dc352907fc980b868725387e985d2553aef46df55b57ccc71ec754531c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988159b6e0dd9d4a144b8fe3cc36d84746", "guid": "bfdfe7dc352907fc980b868725387e984b4aa72ec15795148d90dd31392dcf26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c144e4cf37d69e4e8c7a780cbdbc088f", "guid": "bfdfe7dc352907fc980b868725387e9857259482afa86e999cd573f0ab407be3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb990beb7109793efcbfdb397f1bcb4f", "guid": "bfdfe7dc352907fc980b868725387e98fc0723faddb6f91e8e5ae958388e4ca7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b71097e3f987fa397c161719c918317", "guid": "bfdfe7dc352907fc980b868725387e9805e1c2a26fab419a965ed2a6c9893d18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee81e04c9553de6d313a64ac316b3eb", "guid": "bfdfe7dc352907fc980b868725387e98e4c54b29b4637c9655501bdfede4c5e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98435c80a20aba3293762c478c0349d84e", "guid": "bfdfe7dc352907fc980b868725387e981ba55fac9a2d43b199ed6655fa2a701e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b57a206bd4d21bd8893f096d3b9f61c", "guid": "bfdfe7dc352907fc980b868725387e98c89c58eb3b200bee35f5d3ec4c110988"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf50dc0520d7a908543dd099055c43d3", "guid": "bfdfe7dc352907fc980b868725387e98cd5d0c84a570347521052e1fb71bf350"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec3dc124270ec0d60fb41b9f5656dab4", "guid": "bfdfe7dc352907fc980b868725387e9841c5f25660316f36f888916c5e0ebcd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5707e0cc5963e51c90bce585f2db084", "guid": "bfdfe7dc352907fc980b868725387e989e9974bfb4626c5bb21cefaba0258304"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e9c541b7f5233503ad54776d628cb8f", "guid": "bfdfe7dc352907fc980b868725387e98ab2f2f00da4182751f1a2603c064572d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984305b0eb48ee9e55653e451366615f34", "guid": "bfdfe7dc352907fc980b868725387e98e007e19cceb8274b52e0be6c311b6a82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98661608a3533c3325623c0eb737b1878a", "guid": "bfdfe7dc352907fc980b868725387e98ce6aed59b60ac99567c8dac3e8ae35e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a971ec80fff0adaad8a25fe4b93e8db", "guid": "bfdfe7dc352907fc980b868725387e9822a1b7d962d0fe0734b7feeee64057cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f21173d08ca3c96a914b8b551785fe1f", "guid": "bfdfe7dc352907fc980b868725387e98d504c2e0cf7debdb07df77154846a105"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd76d1b8d07c1839acecc1fceef40649", "guid": "bfdfe7dc352907fc980b868725387e9802641c2f0f109e2a1bf43df52f258b28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bce8ef35a0deb462feaf7877a2bcd34", "guid": "bfdfe7dc352907fc980b868725387e9854085236992325af64d948700954d523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a73c61adc37485bce2696d9cd85a0a3c", "guid": "bfdfe7dc352907fc980b868725387e98b5e3c49d5b734435c4072ca5a6dfa33a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860bed75cafbc236007ef05b76196e77e", "guid": "bfdfe7dc352907fc980b868725387e9867cdecd1718b6aa16bbe593b6f42a726"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d5db2d719954e7537c832242a441d9a", "guid": "bfdfe7dc352907fc980b868725387e98aace47e25342dcc3e672330359e28806"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1e263ff945550a10f7b617c611e6b60", "guid": "bfdfe7dc352907fc980b868725387e98b133c5013553fb2a3ea5876a2587f0ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d91e64f302457a95775fd44524101aa4", "guid": "bfdfe7dc352907fc980b868725387e986a0cfbdcda24b325ee2a1cb6f917611a"}], "guid": "bfdfe7dc352907fc980b868725387e989f79663bd5b8d1a1cba0dce2da9e95dd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e98e281d8c9034c2cc849785d002b3872ef"}], "guid": "bfdfe7dc352907fc980b868725387e98c2f337f9e4302af7787df668cceb9bed", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c8529dce816c1a42564f4756aded952b", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e981ef8e8e7f57ba0a00a30fe5101e5be80", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}