{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98468a19c40826cc9dce94a41e398b9d33", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a98f43b78fa6f77ccd6e4bff0c9a0d21", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9816e8552770c8e90ef2b415183a0888ab", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d0de2d03e5fb661f9e4e6e774a8701ce", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9816e8552770c8e90ef2b415183a0888ab", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987d47f06dd812403b5f72bbdc4d5cd6b6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989668bae751ef31d04d433d81f9d940a1", "guid": "bfdfe7dc352907fc980b868725387e98266e9c99aa3ed83fe28ae9ba5f24d0e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cc450bc19c08bfadd023439c873d92e", "guid": "bfdfe7dc352907fc980b868725387e989017532d52d81c63529565314059571b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9867b92b56e97420d925b96efed3b54f76", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f06f847f94472c750adbe360395f2e78", "guid": "bfdfe7dc352907fc980b868725387e98ee83719123bb925bf76c346247b6c8f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4de22b57eed72c3989cf89d29085036", "guid": "bfdfe7dc352907fc980b868725387e98d13cd6a7d38f7736dd1ab3f2e6278c14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d48ee1443904e6c0a452aaeec6aa977", "guid": "bfdfe7dc352907fc980b868725387e98d5cee410ba144f631e469d118a6fbe6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987048c823286f517c1c539987cfaebf80", "guid": "bfdfe7dc352907fc980b868725387e9884c9f761bbc45c87e172efe27498a452"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd6c200228e902a3b9f3684f6900b24e", "guid": "bfdfe7dc352907fc980b868725387e9838e3509e8acfc508b8856c70b20de4c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d790bb89f42f40efe4187a81916bd542", "guid": "bfdfe7dc352907fc980b868725387e98a9a855a618e4c7c8b5cea276fc8673e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98612f626b53482cdb6c0f33af75ec22d0", "guid": "bfdfe7dc352907fc980b868725387e987b33b687ed26a8a36955d79f260126b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca2e86cf57ab08c2b2bb08b2c47f4256", "guid": "bfdfe7dc352907fc980b868725387e98117a901f403b65a23433b39f155d9b43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870d2142a565a2b14c6d6fad1ef11950b", "guid": "bfdfe7dc352907fc980b868725387e984bde459d95900af39b8e3e11d30be289"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989388a8deb6b76cd013d4090be676e3f5", "guid": "bfdfe7dc352907fc980b868725387e981dcf2712953d8c20f1d0d1c8462c57b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a1ba83694c6ca6f08b9ad822910ecff", "guid": "bfdfe7dc352907fc980b868725387e982e552499965de047ae4eeabda52c9ea5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f337a27c0552c4719176bb0cc50387b", "guid": "bfdfe7dc352907fc980b868725387e98f118a623b7c0c839a4e8e55bfc93c0bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b14cb6e25c671fe9bfe47a626e0d288", "guid": "bfdfe7dc352907fc980b868725387e98592cf0630bf518993f89bdbf1cfc4d5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a61fe1d6e4644a9c2c2ba9c4cd3350bd", "guid": "bfdfe7dc352907fc980b868725387e98d4c69ef239ca642d91d65710db40e1ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98057d7ffc44ac5d98208b0ef194b18103", "guid": "bfdfe7dc352907fc980b868725387e9883b2d4d8bf8597c572c220b046d8b7ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f40a05f2c5505b4cbfb7e7aac5be97c", "guid": "bfdfe7dc352907fc980b868725387e987ddfd7e2c8eed757a13904f94aea1c36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859a439d159fa0190f3dc081cbe1a9bbe", "guid": "bfdfe7dc352907fc980b868725387e98ecce206d7fb24b99f31162563e1ab959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ddce2f8bfda858ec298d526baeba011", "guid": "bfdfe7dc352907fc980b868725387e98fc296beee3f49245f91f67cd3011222d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980921dd33f11b18e0019569802c33107c", "guid": "bfdfe7dc352907fc980b868725387e98b41376c312102017fcf02717f904e0c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c758d8795a178dc14f2bf390942466da", "guid": "bfdfe7dc352907fc980b868725387e982495a1a353d0a9bca99875652fb3b7f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5995169a8954717e536b5592c007260", "guid": "bfdfe7dc352907fc980b868725387e9839d9bed5ee76376fd034f1c2f0f66d31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98574851a84b8aef2561ed952186cef4ee", "guid": "bfdfe7dc352907fc980b868725387e98071e461e410249596e489317df00c629"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b650f6c1669e9ebafb1e6e6a96db00b", "guid": "bfdfe7dc352907fc980b868725387e98820a1d189928215571e6f9aacc963294"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98960a36d37a12f3234c128242ad48ca0b", "guid": "bfdfe7dc352907fc980b868725387e9881f02f67aca41076ecc0a40022993d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a794dfdd93c7f9e18737920ff2189726", "guid": "bfdfe7dc352907fc980b868725387e98300e7f4dd4a12ccb49438e14a48f7096"}], "guid": "bfdfe7dc352907fc980b868725387e98312c1a77b86fecd19d4f7ef40fdd83cf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e98c5a122ef2ca9489abfbea92724eb6a53"}], "guid": "bfdfe7dc352907fc980b868725387e984690180e0e0f92c9c9b66515a4fc9724", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9808c7fb90eb783338f8a08288b25b4c26", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}