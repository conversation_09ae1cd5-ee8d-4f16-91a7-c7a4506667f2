{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983cb85a65e74935a0f3434dbac6a9746a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989f15147ffded90e3711c6a27a895c5dc", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e72b13990c7c71992f6c2fe85adcf94c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984837fc51fae0158e96e8595d097c3223", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e72b13990c7c71992f6c2fe85adcf94c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987f3522ab63eaa9178aa0b47b4fb04f71", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98727508d80ece416a39200f8293bf3908", "guid": "bfdfe7dc352907fc980b868725387e98504e1d8576c16a2227ced1aa8162e5e3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e455b3478866dd0b4efec348821dc1c4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987c90200c74f9b9b0ef67609e032c4255", "guid": "bfdfe7dc352907fc980b868725387e98d8180d717e017a99f6cae9127bb8cefe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98358c7ac973e037e7ec1f14ab5f5c5cb8", "guid": "bfdfe7dc352907fc980b868725387e984687e15c4aaf0431f4e27a8c1cdad708"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed7fe887d6143f38dd6a805fbafb9c68", "guid": "bfdfe7dc352907fc980b868725387e983dc7af03e36caf09827cf388c2329b74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98744cf7a89fe59ca8b33d68a5e515261b", "guid": "bfdfe7dc352907fc980b868725387e986a828afa5dd530b0e1dc740861364f1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832a5288526a4955c96f9da1d7047e4f4", "guid": "bfdfe7dc352907fc980b868725387e98f5017ca37182071acc0c1107cede6cba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98654f0915f261218b04e55f36d45c21bd", "guid": "bfdfe7dc352907fc980b868725387e98f947aea90cf95aedda9abf3ed493bfb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e925795d916758c126b56f17a31c89d", "guid": "bfdfe7dc352907fc980b868725387e98e9d0c09b9dd8f1144551a7e5498ad733"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c96ea74b8e37100287f74d4ec1123017", "guid": "bfdfe7dc352907fc980b868725387e9888e9ba0923c62b5e9ffa26183cc43ebd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987841256d092119c65a0eaaed98c25a88", "guid": "bfdfe7dc352907fc980b868725387e98483c2566f9d5d2f7edaf1e6459ef06ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b62f6db97f9e44f07e9c8357bd68ba0f", "guid": "bfdfe7dc352907fc980b868725387e9859c9a374a20261c96d187609bbb857d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa6866e657551b2f1753f6a8f565de3d", "guid": "bfdfe7dc352907fc980b868725387e989b4dbfc9c3a884a46808c143d6d6787d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858699ed113a6e32d2af47e3f2904298f", "guid": "bfdfe7dc352907fc980b868725387e9894351867b84bc797ded4762265197540"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de0f883877f372a88e0de95f0211c39f", "guid": "bfdfe7dc352907fc980b868725387e982992aa22d07c7e196334c95da205e0fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826b6160c9e327b22be85fa150bbfe472", "guid": "bfdfe7dc352907fc980b868725387e98586c8b53bd20a6da52e5470c71f08e8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98315927b804fef9c04c6e3b0145e548dd", "guid": "bfdfe7dc352907fc980b868725387e98ff289906da1d79fc92591bcec055747c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8a2a04e82837b90972c8051ee0ade75", "guid": "bfdfe7dc352907fc980b868725387e982c9d8a8a7218b042b727e1c191b3130d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98645eae370926376ddfbd4b3a59b4b9f2", "guid": "bfdfe7dc352907fc980b868725387e9838158ee4335eef3df5627a40225d2821"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98991a00e7f60331dd5dbb5440f451704b", "guid": "bfdfe7dc352907fc980b868725387e984e1681c92d95ea083856d1a0f79716e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7cd9d8d2b6f226109e163f7e72fe8e1", "guid": "bfdfe7dc352907fc980b868725387e98add573bc3f33a7232bbe0198d0a04561"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982639bfd297675e39b33a0e06310adfb4", "guid": "bfdfe7dc352907fc980b868725387e98d1b15637cb3869e3d314e81205649723"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f381b4c6a735c0678d0aee426ac784ee", "guid": "bfdfe7dc352907fc980b868725387e98caba826cea3cf8457535c7cf7190507a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845c75f12c986c33222da6fd47530cff6", "guid": "bfdfe7dc352907fc980b868725387e98d35c27155a4f8d48264552ff8811e36b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820e42ca9eceb2d6ee30cdeeb7114f82f", "guid": "bfdfe7dc352907fc980b868725387e9860dda611e92753e1ad820a69aeb43421"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a040d617c022518e24eba46be1220ce", "guid": "bfdfe7dc352907fc980b868725387e9868b72d4ab22fd3b0b8de954dfdabc0c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a382e12ba69601813ac9ef96cf8d09f7", "guid": "bfdfe7dc352907fc980b868725387e98a5927183f115b7512e8d54bd83ea6b3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d01c820fd13dfc961e707346d750566c", "guid": "bfdfe7dc352907fc980b868725387e98e94663bd25da815ad619a3502cee79af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985461887e42e2d1ccba80b279179a97e1", "guid": "bfdfe7dc352907fc980b868725387e98f6fbe3c03b1efa7bf0b975cf856bb4b0"}], "guid": "bfdfe7dc352907fc980b868725387e98be87788cc7bf5b947dfecca834dee60e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980345f55140ab90596d4e4e61753d4de9", "guid": "bfdfe7dc352907fc980b868725387e98c4c7675f22febb4f6cb183ecfef83c5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895a006826061278f3fc3d68b7a486775", "guid": "bfdfe7dc352907fc980b868725387e98402fc830ac26345fbfb321b060841d1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e98fd55531c67c311c92fe07f90e4517c66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d524988b2df3d3e5ae7b40ac7b01c3c", "guid": "bfdfe7dc352907fc980b868725387e98b4f238d9abc7635e923eae2d03505538"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf7730448d7702408dd843541950bbde", "guid": "bfdfe7dc352907fc980b868725387e98a05caaceeb0c87392a026eb549eabae5"}], "guid": "bfdfe7dc352907fc980b868725387e988c1a6dba83bc9c75433ca3a0167abce7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986b2f2bddd9a0d75abaa12c0abd6e5380", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98c434f9f49eb5bef071a118f880631ed3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}