{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9823872e52c67d22188aa373126947b089", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d815015ffb41031955d9c4671f842fe8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988c8f0a131992311b1983bd501834d9bc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9836ce0df8f5aa9184cc35ca37e28aa1a6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988c8f0a131992311b1983bd501834d9bc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98457c9c8aaf8daa02a42336471c83aebc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981b4805fd482f6db7397e34ea0c147da3", "guid": "bfdfe7dc352907fc980b868725387e983ce33dccdea0cd2add55a07e7e525fcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821db30f88e79c1675e21d374815a80a8", "guid": "bfdfe7dc352907fc980b868725387e98051761ae42bbd2ec94dfbdfadec7c249", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872fcd081dd1556fb6cacbd16b5587d9d", "guid": "bfdfe7dc352907fc980b868725387e984215e61e7c900b599e9277842e277d5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fc9fd8c0a2b18b929bd3b5a61bcaa36", "guid": "bfdfe7dc352907fc980b868725387e98a35a05316642ced615d9d85ad39a9123", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8a1d4237a1290e78910c356b9e93c51", "guid": "bfdfe7dc352907fc980b868725387e9884d0f2729f24930869750772f7792bbe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ce0d03caaea7cc60161c1bc3ee4e3de", "guid": "bfdfe7dc352907fc980b868725387e98b7575285c6c3cc4d5ce8cb8226485a94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828e1daa06d066af07238defe86cda8d8", "guid": "bfdfe7dc352907fc980b868725387e98e250e69017df4b4db7daea70571719b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b16002ec81bde8ee04c20b747eba7be8", "guid": "bfdfe7dc352907fc980b868725387e9854e5811d41ea393ca1cc2e828db3d683", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edd3fb4ef3a6c8cc5f89d97e29769a91", "guid": "bfdfe7dc352907fc980b868725387e984ea65dc91f505db4b8a73df5f87511b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864afaba26fe511c1d21e755ff226153c", "guid": "bfdfe7dc352907fc980b868725387e98f6e2eea1250b261e31b21bb8e5b58d95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b606eed0ca5e5958f36d9d03b30727fd", "guid": "bfdfe7dc352907fc980b868725387e98d7a076c5473e3ec9ab6b5025c8f5130f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813eea1757b67af0218503493eb018967", "guid": "bfdfe7dc352907fc980b868725387e98d5ce64501daedc2b64739da29388a233", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fecfde6f3169f8c82d0e306dda1e6f01", "guid": "bfdfe7dc352907fc980b868725387e980f8cc489273c41b5b0bf3a468dcdfb3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d86b959b128fca2065a230317ef653", "guid": "bfdfe7dc352907fc980b868725387e98cdbabf388acdc1d0978c51157cc322d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c3c6a1961d6ac192e8c1ab5bd7e9348", "guid": "bfdfe7dc352907fc980b868725387e983a84ed258dea2de58ae42875477fe655"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f9af4b8f50df163705c3c371b7a122a", "guid": "bfdfe7dc352907fc980b868725387e9827738d2b6bb051faac86171cd689e235", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980feaf2a75f44d8617247c6d1db00e9bc", "guid": "bfdfe7dc352907fc980b868725387e98d3a70c1825d9f88607801db4c9a022d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbd32369d7e652026c0697e096fc5275", "guid": "bfdfe7dc352907fc980b868725387e980953719db8af278115cae2a719a19e51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d9a42aaf9101204b06c53c612d4cec3", "guid": "bfdfe7dc352907fc980b868725387e98a67750cdb5ef09c5a99248899791e376", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dd2566ed1c93d8e085a39956823aab3", "guid": "bfdfe7dc352907fc980b868725387e9856a66d9b83df12bece218201fa6a706a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98281b7ccad2c11d73be074cc15db677b1", "guid": "bfdfe7dc352907fc980b868725387e988640f6a69a00f0b22e33ca25215701ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985443c08112786219ad5e7ef21d8182c9", "guid": "bfdfe7dc352907fc980b868725387e9849baab866323a815b01f33afd15d20c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807d7b0eea2660c2803a8ff48a7204b54", "guid": "bfdfe7dc352907fc980b868725387e98f2dd6dda480a96218c77b93273399ca6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec406f391f1103a10125f5ad2b14dad8", "guid": "bfdfe7dc352907fc980b868725387e98fadb8dd383210aaa88230841324af110"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e219a64ca198345ecd7dfb203750677", "guid": "bfdfe7dc352907fc980b868725387e984c162fc6f69f9719f52970e632c6248d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4af27e8842fd8e15c022c19804622c3", "guid": "bfdfe7dc352907fc980b868725387e98c9e1e11d51e5863e7ea7fe5b9f303957"}], "guid": "bfdfe7dc352907fc980b868725387e984e058ff6e9f41a2f7657348ba5968d27", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98628d77a6756e8c06e0b079084c06cf75", "guid": "bfdfe7dc352907fc980b868725387e98348fda18859d6e518f3e76f83c2b8e1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ab917d48f8cf428c44f7278cbdbe2d7", "guid": "bfdfe7dc352907fc980b868725387e98609f56073578924daece7788c142fa71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0166956f4f1a3d24acafe10e23f4134", "guid": "bfdfe7dc352907fc980b868725387e98fe337db5522dc08aa27094559f373377"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f12046d2630dac17a8e3abbaa399b7c", "guid": "bfdfe7dc352907fc980b868725387e98bef69e611ca7ce0fd649490d5539be7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98672b8540cf27503f35b3e992332a35e7", "guid": "bfdfe7dc352907fc980b868725387e98b06281cc94ec720ebef624c9a216c70a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db20d538db841f220ab3b2213bc729f3", "guid": "bfdfe7dc352907fc980b868725387e98a945d09e5a669edacb9c9e23c0688409"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819981b48f53c581e4ee161a563282f52", "guid": "bfdfe7dc352907fc980b868725387e9863508f8ee6c961575b5d0601d9264aa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc6388ac67f560785a089a4af9d7c8a5", "guid": "bfdfe7dc352907fc980b868725387e98d98cd115acfd5cf806af714f49b933d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98520b5d1801b261732febfada6e5cddfe", "guid": "bfdfe7dc352907fc980b868725387e980cd408017792f4014c90c26df6667014"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b371395aecf8340c0c49f778b5db8f5", "guid": "bfdfe7dc352907fc980b868725387e98b8e59d54dddbb2af1eae9f512a7f46b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d8cf5f6b1f4d1798676687e7c3c403b", "guid": "bfdfe7dc352907fc980b868725387e98ddda4d663350877f472765919774a311"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987087d4adaee040cbfbe13309f5f0c2de", "guid": "bfdfe7dc352907fc980b868725387e9818c78f083322d4e81145813817a42fbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd5bd1647d4bef32b8cde53e982d7cd1", "guid": "bfdfe7dc352907fc980b868725387e9854700f86bc368ce8fe43972db8c57e59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d3b6bf9dc39e6d4d35489218e5fcc47", "guid": "bfdfe7dc352907fc980b868725387e980b5f35f10ff1f09fb1a115a1387e21d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0029e817a80efe6eabef0f329486d25", "guid": "bfdfe7dc352907fc980b868725387e98a02805599958488e719a51ad1d83c33f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802ebe4fb545844df1e8543ce0681f641", "guid": "bfdfe7dc352907fc980b868725387e9876e2e3d404176a7a694af4ed3b367650"}], "guid": "bfdfe7dc352907fc980b868725387e98a1d75e4e65e838692f8740ba0a0cfd9a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e984fe026153dcb30168876ccbffd6bd33a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987baac6cb734d714618ceee4b0f8a57d6", "guid": "bfdfe7dc352907fc980b868725387e98701efbf3d26114f732bd7bafbfcec1df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807865304d85b81a8b4fcd5845f28582c", "guid": "bfdfe7dc352907fc980b868725387e98f66a825a42d283395383ac3247c6b3b5"}], "guid": "bfdfe7dc352907fc980b868725387e983ae0c7fa64d4e2a2cf6916063d64c61d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a640ff4d8415e1dc1f4cdc71ab5192cc", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e983915fa07daf13c4f34bb07cf16772fb4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}