{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d75b8a316af8b05352d04d9cdb7e215e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98255a9b8d72f8b792c02a3cbb8c587ce4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd0a7b0fcacc74241ac6f2f12189092a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985370522ac62dff45fa43e70cb754f2c7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd0a7b0fcacc74241ac6f2f12189092a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ef95b972bbc80234ce3ebc6879ea915d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984f7da31013430e3a09dc251af36c5031", "guid": "bfdfe7dc352907fc980b868725387e98c5ee6aaa1243a71baf6c58afc20628ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867535e57c952d508751300c63cad6cc8", "guid": "bfdfe7dc352907fc980b868725387e98bcc06dcd9ceac42173e5dcf039d15272", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983476eef01d1f76be624c8867afce70c5", "guid": "bfdfe7dc352907fc980b868725387e983d10524f87ef8b3dfaa4e041b3ab9360"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987245af3dabaec1aee6751acdf27cd6a0", "guid": "bfdfe7dc352907fc980b868725387e98112ae00f0af9686b6fee1daad251b90e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac0b7379a24234e41207a9bec889dace", "guid": "bfdfe7dc352907fc980b868725387e988dfe26e8c3efa0c4a501d848919f9352"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aecf97839e6652ac11259ebd0e8c7411", "guid": "bfdfe7dc352907fc980b868725387e98b6dc8e485a8d8caadac2a1fe57cbb4e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98421625365f06c1060e8039023b318db9", "guid": "bfdfe7dc352907fc980b868725387e98888e1ff033bcfee86d15040d6963fcae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d5e2ce357ccb01d09933386bd2fc57a", "guid": "bfdfe7dc352907fc980b868725387e988523ca076f919ce5231c767d134c2a2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eee2ca70cbf4d4f514a22ddc4f0ba2f4", "guid": "bfdfe7dc352907fc980b868725387e98becf3f036279ba9253f58051a7427055", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98503a8293775a93e1120c1395e69fe1a7", "guid": "bfdfe7dc352907fc980b868725387e98fb62e773bc6e9ada573e3c69d1a0c441"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98179312f13e55fe5de9add095818e388b", "guid": "bfdfe7dc352907fc980b868725387e9889090c831235149dd7d60962a9a29d1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d73c81d01ceeb9ea92f37db57867d0e1", "guid": "bfdfe7dc352907fc980b868725387e98b6e7d459436b906a793e45c42d184543", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eee5e118287f88d1a69f891c44909131", "guid": "bfdfe7dc352907fc980b868725387e984acfc143332159966e21fa8d5a7f9570"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d445d62e4ebe23120dbbc2c4a94450f7", "guid": "bfdfe7dc352907fc980b868725387e981e37e34394997174b668ee82c7c6afa0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848cc95988886dcc2b275d89c08ecf36e", "guid": "bfdfe7dc352907fc980b868725387e9868918ef9582eb441d521a92619ec85c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b43cad66f257602f2898eff0315768e", "guid": "bfdfe7dc352907fc980b868725387e98e2ed7a1314dedd28ebaa58fd66dcef56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2a9b603864f52e6897eb4ca3f9fe33f", "guid": "bfdfe7dc352907fc980b868725387e982b14f80707d597595e0ba7360c091bb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834a307cf2f9bfdb9ac469d2c03cb231b", "guid": "bfdfe7dc352907fc980b868725387e981c13bb8df4a4f4a61d038d4aba95286d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebe5fbf824c3464a1ea05dfde1f80120", "guid": "bfdfe7dc352907fc980b868725387e980239c4b791ab08f897716f5338585df7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b5333214b3269c3f37b5fe3c77c1165", "guid": "bfdfe7dc352907fc980b868725387e9804216a443ce8a88826ae99b5127943ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ece70eaaa09c4aaeefd68799a031d65", "guid": "bfdfe7dc352907fc980b868725387e9891d669ca39c96600c18076498a1e3e70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3c1491a7e9ee5666391622bb374135a", "guid": "bfdfe7dc352907fc980b868725387e987535f61249a0617dce300b0227d7f6bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852904af21f71b71e13b73facd0d7acca", "guid": "bfdfe7dc352907fc980b868725387e9896482df4a7fe0bb2efa23b0a014c495e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988da145fc4dbacc27cb8edbd6a1b4a243", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9897082f4300d2e69737d6a61680d68419", "guid": "bfdfe7dc352907fc980b868725387e987c0a41265cc8dd4a13f3140e272324fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da22381b4f7e55ee8dc7170a1763fdde", "guid": "bfdfe7dc352907fc980b868725387e980c34061a41ee3d8ec54fa2b408cd1f9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981362ce4641ea265bf7c911c3b40abf40", "guid": "bfdfe7dc352907fc980b868725387e9826ddadea865af1e7081f40ca6158fd13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98888d87e452b38e24f5b20c144869ae63", "guid": "bfdfe7dc352907fc980b868725387e98f2bd22dfcd50f0323f81c71f02ca7a50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e8d7bd23c214ffe1f4015a7cd3fa019", "guid": "bfdfe7dc352907fc980b868725387e986fc305bb0fb3bdb81cf77ced33b5b4ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b459b39f7398a37fe36bcf101eb9c020", "guid": "bfdfe7dc352907fc980b868725387e98b483b34f20c8898a5c16622d3afafd35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a204e1cfef40c43c92064c7b71d97f2", "guid": "bfdfe7dc352907fc980b868725387e988ad9e7533b131ea535a9841ecc2bf3c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885fbd6d6599e49c7d44c1e6b6c8aa3cb", "guid": "bfdfe7dc352907fc980b868725387e98d393d71cedd80e8e80db9482af22594b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dcc87a200d9cd7cf565e03904a4d5a4", "guid": "bfdfe7dc352907fc980b868725387e98ea18b690729ebed5836f47242599f4bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dac5b0cb8cd6bcc89c4ab590178aab41", "guid": "bfdfe7dc352907fc980b868725387e98c288b576536fa6bde48de52876285dcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861043e424c4c02267506eac9204df04f", "guid": "bfdfe7dc352907fc980b868725387e98b402b9b86c98fcefd6aca1cb4d2438b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cea15832e352552bfd681e1877d13bd", "guid": "bfdfe7dc352907fc980b868725387e98565c1efbeceb3d2dc934e61283c51142"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f226777608ab7305d124611a0d9cd0a", "guid": "bfdfe7dc352907fc980b868725387e98facbb4b45dbb03252a5aba84b0fc2358"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861f05036789601bbd4a4e8202464eea8", "guid": "bfdfe7dc352907fc980b868725387e9841f77fce4db74e145c87671b37cddbf7"}], "guid": "bfdfe7dc352907fc980b868725387e98772efc7482af0eb1613724655aceda6f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e988837799663c4fc00ffdd1b3ac4d828db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf7730448d7702408dd843541950bbde", "guid": "bfdfe7dc352907fc980b868725387e980632215afd40e78746710b144c6613f9"}], "guid": "bfdfe7dc352907fc980b868725387e9850378bb063e17c6b370a70c80edc8ffe", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b53637b558211c615bf39ddad48bc811", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98f0c439e1ddeaa0878bcd44f5da594382", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}