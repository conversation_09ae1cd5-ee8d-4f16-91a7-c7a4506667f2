{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98619e00775a718542a2911212c838974c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseDynamicLinks", "PRODUCT_NAME": "FirebaseDynamicLinks", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989ab54097984711a5a5794a2fde36afaa", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ab22be21010cc700ee0ef7626dec2494", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks.modulemap", "PRODUCT_MODULE_NAME": "FirebaseDynamicLinks", "PRODUCT_NAME": "FirebaseDynamicLinks", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808f84bfd594e8ec260a2cd3f00e7e8be", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ab22be21010cc700ee0ef7626dec2494", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks.modulemap", "PRODUCT_MODULE_NAME": "FirebaseDynamicLinks", "PRODUCT_NAME": "FirebaseDynamicLinks", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b5c486746ed59b0ba13dc03374eac089", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9873631bb16916fae650ecc7e2317f0fc9", "guid": "bfdfe7dc352907fc980b868725387e98304d373e6276cecd79d65659666f2452"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e933147b34f73e214a29b29fd973ad2a", "guid": "bfdfe7dc352907fc980b868725387e982416501e8081b76e8b3ad01dc4158b4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9a8a9c3be2555537f728bf2aa0ffca1", "guid": "bfdfe7dc352907fc980b868725387e988c2762155787a09a6bb14631b7bb97f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833a0561a2afa7d6017c17e1ed80535eb", "guid": "bfdfe7dc352907fc980b868725387e9893c6197a1a0cf47a91423e8df43f1ca1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986aad93d98b613a6ed7ef9ad9ab3ac875", "guid": "bfdfe7dc352907fc980b868725387e983b8e6c4cfce4e1d19b46e2b17ecab997"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98660629c03c8fe85b8032c1cdfb62da5f", "guid": "bfdfe7dc352907fc980b868725387e98e87494c04d34dd7d05356b7cc47b6e55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d86260a365a82b1e68b9bea0e0cc0f4", "guid": "bfdfe7dc352907fc980b868725387e9830607f6d10807b0581394fed5d21fd07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1b7a9b64de53b17b74793e960960ac9", "guid": "bfdfe7dc352907fc980b868725387e9804cdfc98854568934a2a613104e692e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3f2cf05b2eaf19cd19003dc1c78df49", "guid": "bfdfe7dc352907fc980b868725387e985afed32155ca72062f82e1b378bb75b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b958f72661c4fa0feec0ea2f9c9bdf01", "guid": "bfdfe7dc352907fc980b868725387e9889fe9ae7b3d3d64beef3e21553c6926a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985658bb7b1e84b706b5623e313f8de1e0", "guid": "bfdfe7dc352907fc980b868725387e98db105c2ef43c3d4bedb6ddb9ac630e49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f31400e4074517487b55b37b8ecacede", "guid": "bfdfe7dc352907fc980b868725387e98b0fc043686305c654279c60179a37981"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849e1000a1582c189dc7c11fa65f301dc", "guid": "bfdfe7dc352907fc980b868725387e98477d87582bd5123405332098e4bdc622"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b8931f9269e389b9d366a9fbdecb1dc", "guid": "bfdfe7dc352907fc980b868725387e98c251c3133c2aaa027036c08be7801c3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984181357f0bcc4576f0308da4f299e6f4", "guid": "bfdfe7dc352907fc980b868725387e98f76b287408fb91ebfbb6d6eab8f0eba1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810990ad0f58c0a663c7c5fe9eb4f4019", "guid": "bfdfe7dc352907fc980b868725387e989fa44f247eccee68103f45ce913af1a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da44190cab4daeb7d151b2afe59d3b19", "guid": "bfdfe7dc352907fc980b868725387e984cd2c53e99e6246d177ea286bf5e4a34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4d0c06b8d06cf56cf2bddae9f8b5511", "guid": "bfdfe7dc352907fc980b868725387e9839f814fc48e545746663accba63a8ba9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea80624fb4dac92579857c62d64d983f", "guid": "bfdfe7dc352907fc980b868725387e9845824b51c23e6890b7e617bdea629092", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98362cd8872d4954104359933dc7e1461d", "guid": "bfdfe7dc352907fc980b868725387e98721e11ec123e4728cfc3cc44cf0c4126"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98341c6f9a50b1f88b509ac1de3d23e6d9", "guid": "bfdfe7dc352907fc980b868725387e98fb7b24444a36838924955e0a800e5e9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a41c80241b663edcde6052eb047a814", "guid": "bfdfe7dc352907fc980b868725387e98e32221c14d05bf69bbc85688fe617075"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc1c6f8b2f913791b7361aa1b91db1ba", "guid": "bfdfe7dc352907fc980b868725387e985fbd3d4925112e9765fe78a1960bea27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df1a9f946cf1544c9b4a897280510158", "guid": "bfdfe7dc352907fc980b868725387e989b61e3d50d0e0eb8e3fd8c0464e3d8f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8e4803b8312e610986ad32e19c89448", "guid": "bfdfe7dc352907fc980b868725387e98edca9630a1839ff09a37b274f81e9fa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf89b36a3a5bd71bd85b4d066dd522c6", "guid": "bfdfe7dc352907fc980b868725387e98e13e53ee162c39298d1b449c72b7535d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e0195d794bc98a0b0c8f646e5ca9d47", "guid": "bfdfe7dc352907fc980b868725387e98ae483d1d4cc6066ea751e0c56e59dd9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f429e0fb5ab2301bf2e31074ba15b4c8", "guid": "bfdfe7dc352907fc980b868725387e98f53debe584673ba7cb8c15db4749aa2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98223fc99b270316d53308254dc8a9fa27", "guid": "bfdfe7dc352907fc980b868725387e982a10200cbcf87472942fe1aa4f8c2c97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3277c9d30b778945246cfa693b09c1c", "guid": "bfdfe7dc352907fc980b868725387e98bbc5c3e6f0871fa8431842a926045147", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980304e3cf66f0a7299bddd04be7d18bd1", "guid": "bfdfe7dc352907fc980b868725387e988f52617d1769edfcc00e62c915a42c0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee45a1504047428c4d109bb545ec83b", "guid": "bfdfe7dc352907fc980b868725387e9860d5cc4c2863f6063d92ac09958a8ace"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869e27799d1359d3a35ca732d58f0ff9b", "guid": "bfdfe7dc352907fc980b868725387e984cfa20a2d6e3859d0f9fab4d40c4d689"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984724490cbd9f6d49515f31f26edc45d9", "guid": "bfdfe7dc352907fc980b868725387e98638975b67d446a67c56fbcb457f026c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ed6a3621a58c70698d62eff3407fe06", "guid": "bfdfe7dc352907fc980b868725387e98f02246fd0d6ee8fe69eda6dfa6a562c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfdbcd92d72382de6622cb889169875a", "guid": "bfdfe7dc352907fc980b868725387e985abc7dd004fa47d4c75eeca9cb63a0b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b4a67ad040857d815e400e8cef86a50", "guid": "bfdfe7dc352907fc980b868725387e987f76433dfc1eff26e4b762463edf1d08"}], "guid": "bfdfe7dc352907fc980b868725387e9828ef1925c562ff6f4fad54ef3ec4823e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f595752d5908fdd63e79402bf26a67d", "guid": "bfdfe7dc352907fc980b868725387e98d19e11409768fea8137735b11b17bd40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985093658e254dd309a52abc7c14c893ad", "guid": "bfdfe7dc352907fc980b868725387e98092835e3276c65975a08989b89ba08f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851ad5201b32d94ebc722bab753c85639", "guid": "bfdfe7dc352907fc980b868725387e980856ce1ff289ff79f5b084981cfb9248"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894d2eed295fc1f39e6ef915148c35832", "guid": "bfdfe7dc352907fc980b868725387e98e33a953de609637c0d2765047a4a00a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853a3a515448ec92e4111dc8d5fd55698", "guid": "bfdfe7dc352907fc980b868725387e98d1073dcaa0a63d343868bbed1f1541a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a954fd0a6dc93561f497043740798c3", "guid": "bfdfe7dc352907fc980b868725387e98b63c38072a936d62fc24ec55b620c4ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872178614f3f545325d8ceed3c72cb26a", "guid": "bfdfe7dc352907fc980b868725387e98c3a25991ec840b94406b4436eddaa05f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809d58bae4ab39600786c02f8da2a2aad", "guid": "bfdfe7dc352907fc980b868725387e98dacf00925449db13b1642bda9e8cc649"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98268cbf756533601e8a2027c3fe5d5f96", "guid": "bfdfe7dc352907fc980b868725387e988d4baa80dd366d75591b465717edfa75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce48d2abef5ca2afa02b6c08126f621a", "guid": "bfdfe7dc352907fc980b868725387e98ba9d4cf76eb8dd03e63e878d89ef801d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e787b2dd4f8f7a41576b2de7067cb0a", "guid": "bfdfe7dc352907fc980b868725387e98e4b4df9030930dc2c8c93ee4d15649f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873c1e76bbf71a22bdb32f905460b2a1a", "guid": "bfdfe7dc352907fc980b868725387e987ea73cefd3013a6b7846ab51b45cdeb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838f7f5172948047f8853f3b0d2499c47", "guid": "bfdfe7dc352907fc980b868725387e983b59805c33a66135fe5e3c052222fcd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846ba3eb3ecf827208f4f78757a0b17d8", "guid": "bfdfe7dc352907fc980b868725387e98ecdb62dbdfcf12ebe0587197b9683ad2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e06b2763b1d7d3093251c3ed67bdc7dd", "guid": "bfdfe7dc352907fc980b868725387e9811f3a6202a3a474d4b9b42d370375fd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6b93436e202b4761ea0a64d61118527", "guid": "bfdfe7dc352907fc980b868725387e989aed80f35f0ca5455b1ea0444eeaf3a2"}], "guid": "bfdfe7dc352907fc980b868725387e989c23ce4f763db65828f5d74bf240d46c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e98af30323d1780c598c70d6819e028d1c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98467b481b493b6b294a6deba4acbedd82", "guid": "bfdfe7dc352907fc980b868725387e98e9039adcbe73727a7d07b4f254ae2786"}], "guid": "bfdfe7dc352907fc980b868725387e98544d0cb78d0554b74191a139a2221db9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986fb14774bf3d20b75e47d0b9406535bd", "targetReference": "bfdfe7dc352907fc980b868725387e98e58827b88c0abca8fc7e8667fef9326e"}], "guid": "bfdfe7dc352907fc980b868725387e981f4df1a205f0c07389b123bc020a3cf5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98e58827b88c0abca8fc7e8667fef9326e", "name": "FirebaseDynamicLinks-FirebaseDynamicLinks_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98b623f22c0d3d037d0450c736133d3c3e", "name": "FirebaseDynamicLinks", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98834460d3052fa1f4117c6696cee22bc3", "name": "FirebaseDynamicLinks.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}