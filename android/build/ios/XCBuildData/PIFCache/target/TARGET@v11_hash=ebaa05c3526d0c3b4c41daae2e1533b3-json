{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9844e6a1c53165160bb181af9c71544ae5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832f57634461bfc07541306e0e0909c0a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982526715f3469b30b496fbb48e766fe0a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b77ea053d833db80a19dea3c0cec528e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982526715f3469b30b496fbb48e766fe0a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fcf27a1859dbb654e6d1632ecd3da9c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98130b7bc6bdf693ae22dcf612ebebda5a", "guid": "bfdfe7dc352907fc980b868725387e98ba5a719f03637779d0e4114c173859a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dee7b017c6ca4137074bab3d985c657", "guid": "bfdfe7dc352907fc980b868725387e98e2307c7e3b6ededb064e5ddd2a68ec15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2213e2f1dcae7f6046096de02fa2e29", "guid": "bfdfe7dc352907fc980b868725387e982d562db17f8b5b4b452d2545362214e8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869b8a69c0bdd14ad1cdd744b23aa24ae", "guid": "bfdfe7dc352907fc980b868725387e98d04ba8a39c5cad90f8f9d0692576eaa8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f26b694b81efbc61c597bf91845dd8f", "guid": "bfdfe7dc352907fc980b868725387e98c33f235531156ee0adb6b8d65a5c7830", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984401cff28fb23d8e3bab9a465b79373e", "guid": "bfdfe7dc352907fc980b868725387e985a895af414c847d231b5f4da7f1e0eb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841041419d52acc0258f7eeea91577221", "guid": "bfdfe7dc352907fc980b868725387e98a1f1dfde0a897a7a48297b39703ffe87", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf29d9856c061872e08799f2559f84f0", "guid": "bfdfe7dc352907fc980b868725387e98cf56166b008f01c301c175ad0b214b71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848cf58da2164355d78ca9a81d880dc46", "guid": "bfdfe7dc352907fc980b868725387e98d42fe72f176a80ac9bc8f49ca458e7a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986040a04265d0cbcde6a8090c843521f3", "guid": "bfdfe7dc352907fc980b868725387e983f53c7c613094709f31b06d10211e623", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841faf8a1f45b21a71ec6c2c899671396", "guid": "bfdfe7dc352907fc980b868725387e982529dcf37e031935f7df29cb7a0c72be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98844e682ecba32be47440f61aa64a1901", "guid": "bfdfe7dc352907fc980b868725387e98b957187b0e56ff1342b0658816919310", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d89917bb3cf95bb7faa8193429b33a23", "guid": "bfdfe7dc352907fc980b868725387e98d1e1455bdecb88b6dd994789b1a8d005", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984187d6eb507982c9c79c08a7687ecc8b", "guid": "bfdfe7dc352907fc980b868725387e984dc9a9173228d460dce8bd05febcaa21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98221b99265a08079b23b8ba7fccb7e720", "guid": "bfdfe7dc352907fc980b868725387e9866e18cdc65ffb51bafc9666d133d6961", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98069efadc294a8700e06cfd92819fc830", "guid": "bfdfe7dc352907fc980b868725387e9853fba8ec8f30e77eea0ee42d162d7b37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d707c849c6e09bc2172cb0ff0ab398ab", "guid": "bfdfe7dc352907fc980b868725387e9850835d8912cad2b1f1810e1d3a3f127e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980396be9fc4c26e683d5306228162c991", "guid": "bfdfe7dc352907fc980b868725387e98625e8428f767980263004f084f2f06fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825d6f90f2ed2b111e7bf6bff05a57901", "guid": "bfdfe7dc352907fc980b868725387e98c30c5ac2b99a509847068097d1b90b04", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813ed356b88a26594df08f671a355e9a7", "guid": "bfdfe7dc352907fc980b868725387e98d90e924c70c687d95153625ccf22e1b4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9836a2081adbc8385e3ca55444cbc4c87d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98339cfc2ba128c3b98fbaa0d959e1a688", "guid": "bfdfe7dc352907fc980b868725387e986b7cfccea424129119a7e0b7223d203f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff7c7e28cd303fa9586030ccf6d2aa62", "guid": "bfdfe7dc352907fc980b868725387e987e2fd0030cc6bcc3a49952734b8e1436"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0a3a6a767900ffa9ed0f0e4eb80c521", "guid": "bfdfe7dc352907fc980b868725387e989addc411376632e0842314e1f3dbd3eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98582e661320e314146bc0d4c2812e2260", "guid": "bfdfe7dc352907fc980b868725387e98f2b75083ed4223737365239d09129b17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d86360e70391c57bf7e4ff00ccda86cc", "guid": "bfdfe7dc352907fc980b868725387e98f6a65e94218fb5f98f3b3395aafcb285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98746f272c8e0db5d527616b0546c2c405", "guid": "bfdfe7dc352907fc980b868725387e98719f6caf81038a4fb6b36c401e41927f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896cff0e88281eee46eb5b2bde0655fef", "guid": "bfdfe7dc352907fc980b868725387e9897454b0f741f6ecb896065015ddc7084"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d425f9c113fe43bc2f4648e6aebe959", "guid": "bfdfe7dc352907fc980b868725387e980555b502c9c0197b86706e000f128fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6671e4a4c5161d00cbc00db8b53672f", "guid": "bfdfe7dc352907fc980b868725387e9803c38b008504781c8e28657eb9ba9929"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fbe7538b7d2ab28dbcfeb9e623d0298", "guid": "bfdfe7dc352907fc980b868725387e985a2ed91aac5da7e9139f8ae19e8ee587"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb784d9ce95bdddb549317b4c4afb178", "guid": "bfdfe7dc352907fc980b868725387e988ad01aaa6d9324c69a1230aa30684633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c230b89ee0898890d2effe78fce3743", "guid": "bfdfe7dc352907fc980b868725387e9871bc7f2e968b7164541a22f887513a7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985589ae164123248fcb2cfa3687890838", "guid": "bfdfe7dc352907fc980b868725387e98811905a9c4ba0e19039cca05ddec61d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd5d5bd27cb78f3e917970963ac9447d", "guid": "bfdfe7dc352907fc980b868725387e9833f6e0f517dacc5b6398ab313854ccb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fdeff5b82bacf65cae7da5a5e67814b", "guid": "bfdfe7dc352907fc980b868725387e980bb1e730b18176ad52f058cae5417ecf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986101dabeb0cf896d328a702de2587100", "guid": "bfdfe7dc352907fc980b868725387e981af43685ae73254d59946e622f376749"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853ce6afac55e53d75bd1913045b81738", "guid": "bfdfe7dc352907fc980b868725387e98641fcdc650b720a8dfa8fb181fa118f6"}], "guid": "bfdfe7dc352907fc980b868725387e98b1dde1f6d525a703b7caf328c78bea57", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98485fe715e16f2a300f7d3cfb9afb7aaa", "guid": "bfdfe7dc352907fc980b868725387e9844ec617da2f257b0b49d378a54c7b385"}], "guid": "bfdfe7dc352907fc980b868725387e987ca18eb2083c48f78e919a58dc322457", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c95c6b31cc49dcf5ba698c2dfa2917e5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986716a62ee19f61fc991dd3f4aa3e1163", "name": "Mantle.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}