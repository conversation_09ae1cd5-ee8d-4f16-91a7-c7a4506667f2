{"flutter": {"platforms": {"android": {"default": {"projectId": "cp-associates", "appId": "1:900391558375:android:20fc3dbe454a8ab8455642", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "cp-associates", "appId": "1:900391558375:ios:0c75697035c2c4f9455642", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "cp-associates", "configurations": {"android": "1:900391558375:android:20fc3dbe454a8ab8455642", "ios": "1:900391558375:ios:0c75697035c2c4f9455642", "web": "1:900391558375:web:a79b15ec242582f9455642"}}}}}, "firestore": {"database": "(default)", "location": "nam5", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"]}], "hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "storage": {"rules": "storage.rules"}}