const { onCall } = require("firebase-functions/v2/https");
const { onRequest } = require("firebase-functions/v2/https");
const logger = require("firebase-functions/logger");
const functions = require("firebase-functions");
const admin = require("firebase-admin");
const nodemailer = require("nodemailer");
const { FieldValue } = require("firebase-admin/firestore");

admin.initializeApp();
const db = admin.firestore();
const auth = admin.auth();
const messaging = admin.messaging();

var transporter = nodemailer.createTransport({
  host: "smtp.gmail.com",
  port: 465,
  secure: true,
  auth: {
    user: "<EMAIL>",
    pass: "skfbnyjbyotmfvmw",
    // pass: 'zniorhjmhmatlzfd'
  },
});

exports.createUser = onCall(async (request) => {
  // let data = req.body;
  try {
    console.log("IN CREATE TEACHER");
    if (!request.auth)
      return { status: "error", code: 401, message: "Not signed in" };
    let user = await auth.createUser({
      // phoneNumber: request.data.phoneNumber,
      email: request.data.email,
      password: request.data.password,
    });
    console.log("Successfully created new user:", user.uid);
    try {
      let dbUser = await db.collection("users").doc(user.uid).create({
        name: request.data.name,
        email: request.data.email,
        role: request.data.role,
        // createdAt: FieldValue.serverTimestamp(),
      });
      console.log("User Data saved Successfully");
      return { success: true, msg: dbUser };
    } catch (error) {
      console.log("Failed to create user doc, need to delete this user again!");
      await auth.deleteUser(user.uid);
      console.log("User deleted successfully!");
      return { success: false, msg: error };
    }
  } catch (error) {
    console.log("Error creating new user:", error);
    return {
      success: false,
      msg: error["errorInfo"]["message"],
      code: error["errorInfo"]["code"],
    };
  }
});

exports.deleteUser = onCall(async (request) => {
  try {
    await auth.deleteUser(request.data.userId);
    await db.collection("users").doc(request.data.userId).delete();
    return { success: true, msg: "User deleted successfully" };
  } catch (error) {
    return { success: false, msg: error };
  }
});
