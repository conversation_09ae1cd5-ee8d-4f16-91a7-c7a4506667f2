import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/admin/data/firebase_admintask_repo.dart';
import 'package:cp_associates/features/admin/presentation/cubit/admin_task_cubit.dart';
import 'package:cp_associates/features/attendances/data/firebase_attendance_repo.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:cp_associates/features/mastertask/data/firebase_mastertask_repo.dart';
import 'package:cp_associates/features/mastertask/cubit/master_task_cubit.dart';
import 'package:cp_associates/features/project/data/fb_project_repo.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/users/data/firebase_user_repo.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'core/theme/app_theme.dart';
import 'core/widgets/responsive_widget.dart';
import 'features/auth/data/fb_auth_repo.dart';
import 'features/auth/presentation/cubits/auth_cubit.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  MyApp({super.key});

  final firebaseAuthRepo = FirebaseAuthRepo();
  final firebaseProjectRepo = FirebaseProjectRepo();
  final firebaseUserRepo = FirebaseUserRepo();
  final firebaseMasterTaskRepo = FirebaseMasterTaskRepo();
  final firebaseAdminTaskRepo = FirebaseAdminTaskRepo();
  final firebaseAttendanceRepo = FirebaseAttendanceRepo();

  // @override
  @override
  Widget build(BuildContext context) {
    // print("build");
    return true
        ? BlocProvider<AuthCubit>(
          create: (_) => AuthCubit(authRepo: firebaseAuthRepo)..checkAuth(),
          child: BlocBuilder<AuthCubit, AuthState>(
            builder: (context, state) {
              final isAdmin = state.currentUser?.role == "admin";
              return MultiBlocProvider(
                providers: [
                  // BlocProvider<ProjectCubit>(
                  //   // key: ValueKey(DateTime.now()),
                  //   // create: (_) => ProjectCubit(firebaseProjectRepo),
                  //   create: (context) {
                  //     if (isAdmin) {
                  //       print("inIf");
                  //       return ProjectCubit(firebaseProjectRepo)
                  //         ..fetchProjects()
                  //         ..totalProjectCount();
                  //     } else {
                  //       print("inElse");
                  //       return ProjectCubit(firebaseProjectRepo)
                  //         ..fetchProjectsOfCurrentUser()
                  //         ..totalProjectCount();
                  //     }
                  //   },
                  // ),
                  BlocProvider<MasterTaskCubit>(
                    create: (_) => MasterTaskCubit(firebaseMasterTaskRepo),
                  ),

                  BlocProvider<UserCubit>(
                    create:
                        (_) =>
                            UserCubit(firebaseUserRepo, firebaseAuthRepo)
                              ..fetchAllUsers(),
                  ),
                  BlocProvider<AttendanceCubit>(
                    create:
                        (_) => AttendanceCubit(firebaseAttendanceRepo)
                          ..listenToLastPunch(
                            FBAuth.auth.currentUser?.uid ?? '',
                          ),
                  ),
                  if (isAdmin)
                    BlocProvider<AttendanceAdminCubit>(
                      create:
                          (_) =>
                              AttendanceAdminCubit(firebaseAttendanceRepo)
                                ..fetchActiveRequests()
                                ..fetchAllUserToadyPunchesRecord(),
                    ),
                  BlocProvider<AdminTaskCubit>(
                    create:
                        (_) =>
                            AdminTaskCubit(firebaseAdminTaskRepo)
                              ..fetchAllAdminTask(),
                  ),
                  if (isAdmin)
                    BlocProvider<ProjectCubit>(
                      create:
                          (_) =>
                              ProjectCubit(firebaseProjectRepo)
                                ..fetchProjects()
                                ..totalProjectCount(),
                    )
                  else
                    BlocProvider<ProjectCubit>(
                      create:
                          (_) =>
                              ProjectCubit(firebaseProjectRepo)
                                ..fetchProjectsOfCurrentUser()
                                ..totalProjectCount(),
                    ),
                ],
                child: ResponsiveWid(
                  mobile: ScreenUtilInit(
                    designSize: const Size(430, 932),
                    minTextAdapt: true,
                    builder:
                        (_, __) => MaterialApp.router(
                          debugShowCheckedModeBanner: false,
                          theme: AppTheme.lightTheme,
                          routerConfig: appRoute,
                        ),
                  ),
                  desktop: MaterialApp.router(
                    debugShowCheckedModeBanner: false,
                    theme: AppTheme.lightTheme,
                    routerConfig: appRoute,
                  ),
                ),
              );
            },
          ),
        )
        : MultiBlocProvider(
          providers: [
            // BlocProvider<ActivityCubit>(
            //   create: (_) => ActivityCubit(firebaseActivityRepo),
            // ),
            // BlocProvider<BillCubit>(
            //   create: (_) => BillCubit(repo: firebaseBillRepo),
            // ),
            // BlocProvider<DocumentCubit>(
            //   create: (_) => DocumentCubit(documentRepo: firebaseDocumentRepo),
            // ),
            // BlocProvider<TaskCubit>(create: (_) => TaskCubit(firebaseTaskRepo)),
            BlocProvider<AuthCubit>(
              create: (_) => AuthCubit(authRepo: firebaseAuthRepo)..checkAuth(),
            ),

            BlocProvider<ProjectCubit>(
              create: (_) => ProjectCubit(firebaseProjectRepo)..fetchProjects(),
            ),

            BlocProvider<MasterTaskCubit>(
              create: (_) => MasterTaskCubit(firebaseMasterTaskRepo),
            ),
            BlocProvider<AdminTaskCubit>(
              create: (_) => AdminTaskCubit(firebaseAdminTaskRepo),
            ),
            BlocProvider(
              create:
                  (context) =>
                      UserCubit(firebaseUserRepo, firebaseAuthRepo)
                        ..fetchAllUsers(),
            ),

            //attendance
            BlocProvider(
              create: (context) {
                return AttendanceCubit(firebaseAttendanceRepo)
                  ..listenToLastPunch(FBAuth.auth.currentUser?.uid ?? '');
              },
            ),
            BlocProvider(
              create:
                  (context) =>
                      AttendanceAdminCubit(firebaseAttendanceRepo)
                        ..fetchAllUserToadyPunchesRecord()
                        ..fetchActiveRequests(),
            ),
          ],
          child: BlocListener<AuthCubit, AuthState>(
            listener: (context, state) {},
            child: ResponsiveWid(
              mobile: ScreenUtilInit(
                designSize: const Size(430, 932),
                minTextAdapt: true,
                builder:
                    (context, _) => Builder(
                      builder:
                          (routerContext) => MaterialApp.router(
                            debugShowCheckedModeBanner: false,
                            theme: AppTheme.lightTheme,

                            routerConfig: appRoute,
                          ),
                    ),
              ),
              desktop: Builder(
                builder:
                    (routerContext) => MaterialApp.router(
                      debugShowCheckedModeBanner: false,
                      theme: AppTheme.lightTheme,
                      routerConfig: appRoute,
                    ),
              ),
            ),
          ),
        );
  }
}
