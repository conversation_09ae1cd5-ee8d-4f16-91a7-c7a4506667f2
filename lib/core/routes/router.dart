import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/activity/presentation/pages/activity_page.dart';
import 'package:cp_associates/features/admin/presentation/pages/admin_tasks.dart';
import 'package:cp_associates/features/admin/presentation/pages/users_attendance_page.dart';
import 'package:cp_associates/features/attendances/data/firebase_attendance_repo.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_cubit.dart';
import 'package:cp_associates/features/attendances/presentation/pages/attendance_detail_page.dart';
import 'package:cp_associates/features/attendances/presentation/pages/request_page.dart';
import 'package:cp_associates/features/auth/presentation/pages/login_page.dart';
import 'package:cp_associates/features/auth/presentation/pages/registration_page.dart';
import 'package:cp_associates/features/bill/presentation/pages/bill_page.dart';
import 'package:cp_associates/features/document/presentation/pages/document_page.dart';
import 'package:cp_associates/features/home/<USER>/pages/home_page.dart';
import 'package:cp_associates/features/mastertask/presentation/pages/mastertask_page.dart';
import 'package:cp_associates/features/project/data/fb_project_repo.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/project/presentation/pages/project_dasboard.dart';
import 'package:cp_associates/features/project/presentation/pages/projects_page.dart';
import 'package:cp_associates/features/splash/splash_page.dart';
import 'package:cp_associates/features/task/data/firebase_task_repo.dart';
import 'package:cp_associates/features/task/presentation/cubit/task_cubit.dart';
import 'package:cp_associates/features/task/presentation/cubit/taskform_cubit.dart';
import 'package:cp_associates/features/task/presentation/pages/task_detail_page.dart';
import 'package:cp_associates/features/task/presentation/pages/task_page.dart';
import 'package:cp_associates/features/users/presentation/pages/userprofile_page.dart';
import 'package:cp_associates/features/users/presentation/pages/users_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

// late final GoRouter router;

final GoRouter appRoute = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: kIsWeb ? Routes.home : Routes.splash,
  routes: _routes,
  redirect: redirector,
  // errorBuilder: (context, state) => const ErrorPage(),
);
Future<String?> redirector(BuildContext context, GoRouterState state) async {
  if (FBAuth.auth.currentUser != null) {
    if (state.uri.path == Routes.login) {
      return Routes.home;
    }
  } else {
    if (state.uri.path != Routes.login) {
      return Routes.login;
    }
  }
  return null;
}

List<RouteBase> get _routes {
  return <RouteBase>[
    //Project Routes
    ShellRoute(
      builder: (context, state, child) {
        final projectId = state.pathParameters['id'] ?? "";

        return ProjectDetailsPage(projectId: projectId, child: child);
      },
      routes: [
        GoRoute(
          path: "${Routes.activity}/:id",
          pageBuilder: (context, state) {
            final projectId = state.pathParameters['id'];
            return NoTransitionPage(child: ActivityPage(projectId: projectId));
          },
        ),
        GoRoute(
          path: "${Routes.task}/:id",
          pageBuilder: (context, state) {
            final projectId = state.pathParameters['id'] ?? "";
            return NoTransitionPage(child: TaskPage(projectId: projectId));
          },
        ),
        GoRoute(
          path: "${Routes.document}/:id",
          pageBuilder: (context, state) {
            final projectId = state.pathParameters['id'] ?? "";
            return NoTransitionPage(child: DocumentPage(projectId: projectId));
          },
        ),
        GoRoute(
          path: "${Routes.bill}/:id",
          pageBuilder: (context, state) {
            final projectId = state.pathParameters['id'] ?? "";
            return NoTransitionPage(child: BillPage(projectId: projectId));
          },
        ),
      ],
    ),
    //auth routes,
    GoRoute(
      path: Routes.registration,
      pageBuilder:
          (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: RegistrationPage()),
    ),
    GoRoute(
      path: Routes.login,
      pageBuilder:
          (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: LoginPage()),
    ),

    //Common Routes
    GoRoute(
      path: Routes.splash,
      pageBuilder:
          (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: SplashPage()),
    ),

    GoRoute(
      path: "${Routes.taskDetail}/:id",
      pageBuilder: (context, state) {
        final taskId = state.pathParameters['id'] ?? "";
        return NoTransitionPage(
          child: MultiBlocProvider(
            providers: [
              BlocProvider(
                create:
                    (context) =>
                        TaskCubit(FirebaseTaskRepo())
                          ..fetchTaskById(taskId)
                          ..fetchTaskLogs(taskId),
              ),
              // BlocProvider(
              //   create: (context) => TaskFormCubit(FirebaseTaskRepo()),
              // ),
            ],

            child: TaskDetailPage(taskId: taskId),
          ),
        );
      },
    ),

    GoRoute(
      path: Routes.users,
      pageBuilder:
          (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: UsersPage()),
    ),
    GoRoute(
      path: Routes.userAttendance,
      pageBuilder:
          (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: UserAttendancePage()),
    ),
    GoRoute(
      path: Routes.masterTask,
      pageBuilder:
          (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: MasterTaskPage()),
    ),

    GoRoute(
      path: "${Routes.usersDetails}",
      pageBuilder: (context, state) {
        return NoTransitionPage(child: UserProfilePage());
      },
    ),
    GoRoute(
      path: Routes.adminTask,
      pageBuilder:
          (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: AdminTasks()),
    ),
    GoRoute(
      path: Routes.home,
      pageBuilder: (BuildContext context, GoRouterState state) {
        return NoTransitionPage(child: HomePage());
      },
    ),
    GoRoute(
      path: Routes.projects,
      pageBuilder: (context, state) {
        return NoTransitionPage(child: ProjectsPage());
      },
    ),

    //attendace routes
    GoRoute(
      path: "${Routes.attendanceDetail}/:id",
      pageBuilder: (context, state) {
        final userId = state.pathParameters['id'] ?? "";
        return NoTransitionPage(
          child: BlocProvider(
            create: (context) {
              return AttendanceCubit(FirebaseAttendanceRepo())
                ..fetchUserPunches(
                  userId,
                  DateTime(
                    DateTime.now().year,
                    DateTime.now().month,
                    DateTime.now().day,
                  ),
                )
                ..fetchUserMonthlyRecord(
                  userId,
                  DateTime(DateTime.now().year, DateTime.now().month),
                );
              // ..applyFilter();
            },
            child: AttendanceDetailPage(userId: userId),
          ),
        );
      },
    ),
    GoRoute(
      path: Routes.request,
      pageBuilder: (context, state) {
        return NoTransitionPage(child: RequestPage());
      },
    ),
  ];
}

class Routes {
  //auht route
  static const login = '/login';
  static const registration = '/registration';

  //Common Routes
  static const home = '/';
  static const projects = '/projects';

  static const splash = "/splash";
  static const masterTask = '/masterTask';

  //attendance routes
  static const userAttendance = '/userAttendance';
  static const attendanceDetail = '/attendanceDetail';
  static const request = '/request';

  //admin routes
  static const adminTask = '/adminTask';

  //users routes
  static const users = "/users";
  static const usersDetails = "/usersDetails";

  //Side And AppBar Routs
  static const activity = '/activity';
  static const task = '/task';
  static const bill = '/bill';
  static const document = '/document';

  //detail page route
  static const taskDetail = '/taskDetail';
}
