// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAjm7Vk_GRjV5GXh5NJMXz90NG3UnqvnbA',
    appId: '1:900391558375:web:a79b15ec242582f9455642',
    messagingSenderId: '900391558375',
    projectId: 'cp-associates',
    authDomain: 'cp-associates.firebaseapp.com',
    storageBucket: 'cp-associates.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBZ93RcAKeXb9mliBeQPvRvvlsZyne53RI',
    appId: '1:900391558375:android:20fc3dbe454a8ab8455642',
    messagingSenderId: '900391558375',
    projectId: 'cp-associates',
    storageBucket: 'cp-associates.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBFEsIrfLtEkZRlth9xEarDJBHapstu4Ow',
    appId: '1:900391558375:ios:0c75697035c2c4f9455642',
    messagingSenderId: '900391558375',
    projectId: 'cp-associates',
    storageBucket: 'cp-associates.firebasestorage.app',
    iosBundleId: 'com.example.cpAssociates',
  );
}
