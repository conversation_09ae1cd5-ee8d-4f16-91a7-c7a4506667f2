import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/widgets/file_preview.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:firebase_storage/firebase_storage.dart';

num calculatePercentage(half, total) {
  return half / (total * 100);
}

//DIALOG  METHODS
Future<dynamic> showConfirmDeletDialog(
  BuildContext context,
  Function onDelete,
  // ProjectModel project,
) {
  return showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: const Text('Confirm Deletion'),
        content: const Text('Are you sure you want to delete ?'),
        actions: <Widget>[
          // Cancel button
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Return false if cancelled
            },
            child: const Text('Cancel'),
          ),
          // Confirm button
          TextButton(
            onPressed: () {
              onDelete();
              Navigator.pop(context);
            },
            child: const Text('Delete'),
          ),
        ],
      );
    },
  );
}

Future<dynamic> showUserConfirmDeletDialog(
  BuildContext context,
  Function onDelete,
  // ProjectModel project,
) {
  return showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: const Text('Confirm User Deletion'),
        content: TextFormField(
          decoration: InputDecoration(
            hintText: "Type password to confirm deletion",
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.grey2),
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Enter password';
            }
            if (value != '1213') {
              return 'Enter correct password';
            }
            return null;
          },
          autovalidateMode: AutovalidateMode.onUserInteraction,
        ),

        // content: const Text('Are you sure you want to delete ?'),
        actions: <Widget>[
          // Cancel button
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Return false if cancelled
            },
            child: const Text('Cancel'),
          ),
          // Confirm button
          TextButton(
            onPressed: () {
              onDelete();
              Navigator.pop(context);
            },
            child: const Text('Delete'),
          ),
        ],
      );
    },
  );
}

Future<dynamic> showLogoutDialog(
  BuildContext context,
  Function onDelete,
  // ProjectModel project,
) {
  return showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: const Text('Confirm Logout'),
        content: const Text('Are you sure you want to logout ?'),
        actions: <Widget>[
          // Cancel button
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Return false if cancelled
            },
            child: const Text('Cancel'),
          ),
          // Confirm button
          TextButton(
            onPressed: () {
              onDelete();
              Navigator.pop(context);
            },
            child: const Text('Logout'),
          ),
        ],
      );
    },
  );
}

Future<bool?> viewImageDialog(
  BuildContext context,
  SelectedImage? uploadImage,
  String? dbImage,
  bool isEditing,
) async {
  final res = await showDialog(
    context: context,
    builder: (context) {
      return StatefulBuilder(
        builder: (context, setState2) {
          return Dialog(
            // backgroundColor: Colors.transparent,
            child: Container(
              width: kIsWeb ? 500 : null,
              height: kIsWeb ? 500 : null,
              clipBehavior: Clip.antiAlias,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
              ),
              child:
                  uploadImage != null
                      ? Image.memory(
                        uploadImage.uInt8List,
                        width: double.maxFinite,
                        fit: BoxFit.cover,
                      )
                      : InteractiveViewer(
                        child: CachedNetworkImage(
                          imageUrl: dbImage ?? "",
                          width: double.maxFinite,
                          fit: BoxFit.cover,
                          placeholder:
                              (context, url) => Container(
                                width: 500,
                                height: 500,
                                alignment: Alignment.center,
                                child: const CircularProgressIndicator(
                                  color: AppColors.primary,
                                  strokeWidth: 2,
                                ),
                              ),
                          errorWidget:
                              (context, url, error) => Container(
                                width: 400,
                                height: 400,
                                color: Colors.grey[200],
                                child: const Icon(Icons.error),
                              ),
                        ),
                      ),
            ),
          );
        },
      );
    },
  );
  return res;
}

//FILES METHODS
String checkExtenstion(String? extenstion) {
  if (extenstion?.toLowerCase() == docTypes.PDF.toLowerCase()) {
    return docTypes.PDF;
  } else if (imageExtensions.contains(extenstion?.toLowerCase() ?? "")) {
    return docTypes.Images;
  } else {
    return docTypes.Others;
  }
}

Widget buildFilePreview({
  required BuildContext context,
  required SelectedImage? selectedFile,
  required String? dbFile,
  required String? dbFileExt,
  required String? dbFileName,
  required bool isEdit,
  required VoidCallback onDelete,
  required VoidCallback onView,
  required bool isMessage,
}) {
  if (selectedFile == null && dbFile == null) return const SizedBox();

  final extension = selectedFile?.extension?.toLowerCase();
  final fileType =
      selectedFile != null
          ? checkExtenstion(extension)
          : checkExtenstion(dbFileExt);

  return FilePreviewContainer(
    fileType: fileType,
    dbFile: dbFile,
    dbFileExt: dbFileExt,
    dbFileName: dbFileName,
    selectedFile: selectedFile,
    isEdit: isEdit,
    onDelete: onDelete,
    onView: onView,
    isMessage: isMessage,
  );
}

Future<void> viewFile({
  required BuildContext context,
  required dynamic selectedFile,
  required String? dbImg,
  required String? dbImgExt,
}) async {
  final ext = selectedFile?.extension ?? dbImgExt ?? "";
  final fileType = checkExtenstion(ext);

  if (selectedFile != null) {
    if (fileType == docTypes.PDF) {
      // Navigator.push(
      //   context,
      //   MaterialPageRoute(
      //     builder: (_) => FileView(selectedFile: selectedFile, dbFile: null),
      //   ),
      // );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Preview not supported for this file type.')),
      );
    } else if (fileType == docTypes.Images) {
      await viewImageDialog(context, selectedFile, null, true);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Preview not supported for this file type.')),
      );
    }
  } else {
    // Fb file handling
    if (fileType == docTypes.PDF) {
      kIsWeb
          ? launchUrl(Uri.parse(dbImg ?? ""))
          : Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (_) => Scaffold(
                    appBar: AppBar(title: Text("View PDF")),
                    body: SfPdfViewer.network(dbImg ?? ""),
                  ),
            ),
          );
    } else if (fileType == docTypes.Images) {
      await viewImageDialog(context, null, dbImg, true);
    } else {
      if (dbImg != null) {
        await launchDownloadUrl(context, dbImg);
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('No download link available.')));
      }
    }
  }
}

Future<void> launchDownloadUrl(BuildContext context, String url) async {
  final Uri uri = Uri.parse(url);
  if (await canLaunchUrl(uri)) {
    await launchUrl(
      uri,
      mode: LaunchMode.externalApplication,
    ); // Opens in browser or external app
  } else {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Could not launch download URL.')));
  }
}

//COLORS METHODS
Color statusColor(String status) {
  switch (status) {
    case ProjectStatus.finished:
      return Color(0xFF1B75D0);
    case ProjectStatus.active:
      return Color(0xFF499B21);
    case ProjectStatus.onHold:
      return Color(0xFFC37B34);
    default:
      return Colors.transparent;
  }
}

Color priorityStatusColor(String status) {
  switch (status) {
    case TaskSPrioritytatus.high:
      return Color(0xFFFFB0B0);
    case TaskSPrioritytatus.mid:
      return Color(0xFFFFE1B0);
    case TaskSPrioritytatus.low:
      return Color(0xFFA4ED80);
    default:
      return Colors.transparent;
  }
}

Color getColorFromInput(String input) {
  if (input.isEmpty) return Colors.black;

  final int? number = int.tryParse(input);

  if (number != null) {
    if (number >= 1 && number <= 3) {
      return const Color(0xFFFF9963); // 1–3
    } else if (number >= 4 && number <= 7) {
      return const Color(0xFF46CE7A); // 4–7
    } else if (number >= 8 && number <= 10) {
      return const Color(0xFFB1B338); // 8–10
    } else {
      return Colors.black; // Out of range
    }
  }

  String upper = input.toUpperCase();
  int charCode = upper.codeUnitAt(0);

  if (charCode < 'A'.codeUnitAt(0) || charCode > 'Z'.codeUnitAt(0)) {
    return Colors.black;
  }

  if (charCode >= 'A'.codeUnitAt(0) && charCode <= 'I'.codeUnitAt(0)) {
    return const Color(0xFFFF9963); // A–I
  } else if (charCode >= 'J'.codeUnitAt(0) && charCode <= 'R'.codeUnitAt(0)) {
    return const Color(0xFF46CE7A); // J–R
  } else {
    return const Color(0xFFB1B338); // S–Z
  }
}

//STRINGS METHODS
String truncateText(String text, dynamic length, {omission = '...'}) {
  if (length >= text.length) {
    return text;
  }
  return text.replaceRange(length, text.length, omission);
}

String generateRandomId() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  final rand = Random.secure();
  return List.generate(6, (index) => chars[rand.nextInt(chars.length)]).join();
}

//DATES METHODS
String getDateLabel(DateTime date) {
  final now = DateTime.now();
  final today = DateTime(now.year, now.month, now.day);
  final yesterday = today.subtract(Duration(days: 1));

  if (date == today) return 'Today';
  if (date == yesterday) return 'Yesterday';

  // Otherwise return formatted date
  return date.goodDayDate();
}

extension MetaWid on DateTime {
  String goodDate() {
    try {
      return DateFormat.yMMMM().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodDayDate() {
    try {
      return DateFormat.yMMMMd().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String convertToDDMMYY() {
    DateFormat formatter = DateFormat('dd-MM-yyyy');
    return formatter.format(this);
  }

  String goodTime() {
    try {
      return DateFormat('hh:mm a').format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String convertToDDMMYYYYSlashes() {
    try {
      return DateFormat('dd/MM/yyyy').format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }
}

SettableMetadata metaDataGenerator(SelectedImage imageFile) {
  try {
    // Determine file type based on extension
    final extension = imageFile.extension?.toLowerCase() ?? '';
    final imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];

    if (extension == 'pdf') {
      return SettableMetadata(
        contentDisposition: 'inline',
        contentType: 'application/pdf',
      );
    } else if (imageExtensions.contains(extension)) {
      return SettableMetadata(
        contentDisposition: 'inline',
        contentType: 'image/$extension',
      );
    } else {
      return SettableMetadata();
    }
  } on Exception catch (e) {
    debugPrint(e.toString());
    return SettableMetadata();
  }
}
