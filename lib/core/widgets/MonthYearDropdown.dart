import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class MonthYearDropdown extends StatefulWidget {
  final void Function(DateTime selectedMonth) onMonthSelected;

  const MonthYearDropdown({Key? key, required this.onMonthSelected})
    : super(key: key);

  @override
  _MonthYearDropdownState createState() => _MonthYearDropdownState();
}

class _MonthYearDropdownState extends State<MonthYearDropdown> {
  String? _selectedLabel;
  final List<String> _monthNames = List.generate(
    12,
    (i) => DateFormat.MMMM().format(DateTime(0, i + 1)),
  );

  List<Map<String, dynamic>> _generateMonthYearItems() {
    final now = DateTime.now();
    final currentMonth = now.month;
    final currentYear = now.year;

    return List.generate(12, (i) {
      final month = i + 1;
      final year = (month > currentMonth) ? currentYear - 1 : currentYear;
      final label = "${_monthNames[i]} $year";

      return {"label": label, "value": DateTime(year, month)};
    });
  }

  @override
  Widget build(BuildContext context) {
    final items = _generateMonthYearItems();

    return DropdownButton<String>(
      hint: const Text("Select Month"),
      value: _selectedLabel,
      items:
          items.map((item) {
            return DropdownMenuItem<String>(
              value: item["label"],
              child: Text(item["label"]),
            );
          }).toList(),
      onChanged: (selected) {
        final selectedItem = items.firstWhere(
          (item) => item["label"] == selected,
        );
        setState(() {
          _selectedLabel = selectedItem["label"];
        });
        widget.onMonthSelected(selectedItem["value"]);
      },
    );
  }
}

class MyPage extends StatelessWidget {
  void _handleMonthSelected(DateTime selectedDate) {
    print("Selected month: ${selectedDate.month}, year: ${selectedDate.year}");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Select Month')),
      body: Center(
        child: MonthYearDropdown(onMonthSelected: _handleMonthSelected),
      ),
    );
  }
}
