import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomTextField extends StatelessWidget {
  CustomTextField({
    super.key,
    required this.controller,
    required this.hintText,
    required this.title,
  });

  final TextEditingController controller;
  String hintText;
  String title;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title.isEmpty ? SizedBox() : Text(title),
        SizedBox(height: 5),
        TextFormField(
          controller: controller,

          decoration: InputDecoration(
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.grey2),
              borderRadius: BorderRadius.circular(10),
            ),
            hintText: hintText,
            hintStyle: TextStyle(color: AppColors.grey2),

            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return title.isEmpty
                  ? "this field is required"
                  : "Enter ${title}";
            }
            return null;
          },
          // validator:
          //     (value) =>
          //         value == null || value.isEmpty
          //             ? 'this field is required'
          //             : null,
        ),
      ],
    );
  }
}

class CustomNumTextField extends StatelessWidget {
  CustomNumTextField({
    super.key,
    required this.controller,
    required this.hintText,
    required this.title,
  });

  final TextEditingController controller;
  final String hintText;
  final String title;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title.isEmpty ? SizedBox() : Text(title),
        SizedBox(height: 5),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.grey),
              borderRadius: BorderRadius.circular(
                10,
              ), // Use your AppColors.grey2
            ),
            hintText: hintText,
            hintStyle: TextStyle(
              color: AppColors.grey2,
            ), // Use your AppTextStyles.label
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Enter $title';
            }
            if (double.tryParse(value) == null) {
              return 'Enter a valid number';
            }
            return null;
          },
          keyboardType: TextInputType.number,
        ),
      ],
    );
  }
}

class CustomFileUploadField extends StatelessWidget {
  Function onTap;
  String title;
  String hintText;
  Icon prefixIcon;
  CustomFileUploadField({
    super.key,
    required this.hintText,
    required this.title,
    required this.onTap,
    required this.prefixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title.isEmpty ? SizedBox() : Text(title),
        SizedBox(height: 5),
        GestureDetector(
          onTap: () async => onTap(),
          child: AbsorbPointer(
            child: TextFormField(
              readOnly: true,
              decoration: InputDecoration(
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey),
                  borderRadius: BorderRadius.circular(10),
                ),
                hintText: hintText,
                prefixIcon: prefixIcon,
                hintStyle: TextStyle(color: Colors.grey),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class CustomDateField extends StatelessWidget {
  final String? hintText;
  final String title;
  Function onTap;
  final String initialValue;

  CustomDateField({
    super.key,
    // required this.taskFormCubit,
    required this.hintText,
    required this.title,
    required this.onTap,
    required this.initialValue,
  });

  // final TaskFormCubit taskFormCubit;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title),
        SizedBox(height: 5),
        TextFormField(
          readOnly: true,
          onTap: () {
            onTap();
            // taskFormCubit.selectStartDate(context);
          },
          decoration: InputDecoration(
            hintText: hintText,

            // state.startDate != null
            //     ? state.startDate?.goodDayDate()
            //     : "start date",
            suffixIcon: IconButton(
              onPressed: () {
                onTap();
                // taskFormCubit.selectStartDate(context);
              },
              icon: Icon(CupertinoIcons.calendar),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.grey2),
              borderRadius: BorderRadius.circular(10),
            ),
            hintStyle: TextStyle(color: AppColors.grey2),

            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
          initialValue: initialValue,
          validator: (value) {
            if (hintText == null) {
              return "date is required";
            }
            return null;
          },
        ),
      ],
    );
  }
}

class CustomDescTextField extends StatelessWidget {
  CustomDescTextField({
    super.key,
    required this.controller,
    required this.hintText,
    required this.title,
    required this.maxChars,
  });

  final TextEditingController controller;
  String hintText;
  String title;
  int maxChars;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title.isEmpty ? SizedBox() : Text(title),
        SizedBox(height: 5),
        TextFormField(
          controller: controller,
          inputFormatters: [LengthLimitingTextInputFormatter(maxChars)],
          decoration: InputDecoration(
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.grey2),
              borderRadius: BorderRadius.circular(10),
            ),
            hintText: hintText,
            hintStyle: TextStyle(color: AppColors.grey2),

            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return title.isEmpty
                  ? "this field is required"
                  : "Enter ${title}";
            }
            return null;
          },
          // validator:
          //     (value) =>
          //         value == null || value.isEmpty
          //             ? 'this field is required'
          //             : null,
        ),
      ],
    );
  }
}

class CustomDropDownField extends StatelessWidget {
  CustomDropDownField({
    super.key,
    required this.title,
    required this.hintText,
    required this.initialValue,
    required this.validatorText,
    required this.items,
    required this.onChanged,
  });
  String title;
  String hintText;
  dynamic initialValue;
  String validatorText;
  Function onChanged;
  List<DropdownMenuItem> items;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title),
        SizedBox(height: 5),
        DropdownButtonHideUnderline(
          child: DropdownButtonFormField(
            value: initialValue,
            validator:
                (value) =>
                    value == null || value.isEmpty ? validatorText : null,
            decoration: InputDecoration(
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.grey2),
                borderRadius: BorderRadius.circular(10),
              ),
              hintText: hintText,
              hintStyle: TextStyle(color: AppColors.grey2),

              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            items: items,
            onChanged: (value) => onChanged(value),
          ),
        ),
      ],
    );
  }
}
