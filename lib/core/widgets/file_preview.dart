import 'package:cp_associates/core/utils/const.dart';
import 'package:flutter/cupertino.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cp_associates/core/theme/app_colors.dart';

class FilePreviewContainer extends StatelessWidget {
  final String fileType;
  final String? dbFile;
  final String? dbFileName;
  final String? dbFileExt;
  final SelectedImage? selectedFile;
  final bool isEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onView;
  final bool? isMessage;

  const FilePreviewContainer({
    super.key,
    required this.fileType,
    this.dbFile,
    this.dbFileName,
    this.dbFileExt,
    this.selectedFile,
    required this.isEdit,
    this.onDelete,
    this.onView,
    this.isMessage,
  });

  @override
  Widget build(BuildContext context) {
    if (dbFile == null && selectedFile == null) return const SizedBox();

    late Widget fileDisplay;

    if (docTypes.Images == fileType) {
      fileDisplay =
          dbFile != null
              ? GestureDetector(
                onTap: onView,
                child: CachedNetworkImage(
                  imageUrl: dbFile!,
                  placeholder:
                      (context, url) => Container(
                        width: 20,
                        height: 20,
                        child: Center(child: const CircularProgressIndicator()),
                      ),
                  width: isMessage == true ? 200 : 50,
                  height: isMessage == true ? 200 : 50,
                  fit: BoxFit.contain,
                ),
              )
              : Image.memory(
                selectedFile!.uInt8List,
                width: 50,
                height: 50,
                fit: BoxFit.cover,
              );
    } else {
      IconData icon =
          fileType == docTypes.PDF
              ? Icons.picture_as_pdf
              : CupertinoIcons.doc_chart;

      final label =
          selectedFile != null
              ? '${selectedFile?.name}.${selectedFile?.extension}'
              : dbFileName ?? 'Unnamed File';

      fileDisplay = Container(
        padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        height: 50,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: AppColors.borderGrey, width: 1.5),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.grey),
            const SizedBox(width: 5),
            Text(
              label,
              style: const TextStyle(color: Colors.black, fontSize: 12),
            ),
          ],
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment:
          isEdit ? MainAxisAlignment.center : MainAxisAlignment.start,
      children: [
        GestureDetector(onTap: onView, child: fileDisplay),
        if (isEdit)
          Container(
            width: 25,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              padding: EdgeInsets.zero,
              alignment: Alignment.center,
              icon: const Icon(
                CupertinoIcons.delete,
                size: 20,
                color: Colors.black,
              ),
              onPressed: onDelete,
            ),
          ),
      ],
    );
  }
}
