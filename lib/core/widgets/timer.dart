import 'dart:async';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:flutter/material.dart';

class TimeDisplayWidget extends StatefulWidget {
  const TimeDisplayWidget({Key? key}) : super(key: key);

  @override
  _TimeDisplayWidgetState createState() => _TimeDisplayWidgetState();
}

class _TimeDisplayWidgetState extends State<TimeDisplayWidget> {
  late DateTime _currentTime;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _currentTime = DateTime.now();
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(minutes: 1), (Timer t) {
      setState(() {
        _currentTime = DateTime.now();
      });
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      _currentTime.goodTime(), // assuming goodTime() is your extension
      style: const TextStyle(color: AppColors.white, fontSize: 16),
    );
  }
}
