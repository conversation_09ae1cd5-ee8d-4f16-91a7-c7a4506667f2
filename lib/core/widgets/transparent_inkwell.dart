import 'package:flutter/material.dart';

class TransparentInkWell extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onHover;
  final VoidCallback? onLongPress;
  final VoidCallback? onDoubleTap;

  const TransparentInkWell({
    Key? key,
    required this.child,
    this.onTap,
    this.onHover,
    this.onLongPress,
    this.onDoubleTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      hoverColor: Colors.transparent,
      focusColor: Colors.transparent,
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      onTap: onTap,
      onLongPress: onLongPress,
      onDoubleTap: onDoubleTap,
      child: child,
    );
  }
}
