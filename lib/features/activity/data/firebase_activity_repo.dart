import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/activity/domain/antity/activity_model.dart';
import 'package:cp_associates/features/activity/domain/repo/activity_repo.dart';

class FirebaseActivityRepo implements ActivityRepo {
  final activityRef = FBFireStore.activities;

  @override
  Future<void> createActivity(ActivityModel activity) async {
    final docRef = activityRef.doc();
    final newActivity = activity.copyWith(docId: docRef.id);
    await docRef.set(newActivity.toJson());
  }

  @override
  Future<void> updateActivity(ActivityModel activity) async {
    await activityRef.doc(activity.docId).update(activity.toJson());
  }

  @override
  Future<void> deleteActivity(String activityId) async {
    await activityRef.doc(activityId).delete();
  }

  @override
  Stream<List<ActivityModel>> fetchProjectActivity(String projectId) {
    return activityRef.where('projectId', isEqualTo: projectId).snapshots().map(
      (snapshot) {
        return snapshot.docs
            .map((doc) => ActivityModel.fromSnapshot(doc))
            .toList();
      },
    );
  }
}
