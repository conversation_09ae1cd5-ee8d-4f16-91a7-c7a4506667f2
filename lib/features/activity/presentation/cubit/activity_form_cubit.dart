// ignore_for_file: unused_import

import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/activity/domain/antity/activity_model.dart';
import 'package:cp_associates/features/activity/domain/repo/activity_repo.dart';
import 'package:cp_associates/features/stroage/data/firebase_storage_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

part 'activity_form_state.dart';

class ActivityFormCubit extends Cubit<ActivityFormState> {
  final ActivityRepo activityRepo;
  ActivityFormCubit(this.activityRepo) : super(ActivityFormState.initial());

  TextEditingController? messageController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  void initializeForm(ActivityModel? editActivity) {
    if (editActivity != null) {
      messageController?.text = editActivity.message ?? '';
      emit(state.copyWith(dbFile: editActivity.attachment));
    } else {
      emit(ActivityFormState.initial());
    }
  }

  Future<void> pickFileFromCamera(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickImageNewCamera(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  Future<void> pickFile(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickFile(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  Future<void> viewPickFile(
    String? dbImg,
    BuildContext context,
    String? dbImgExt,
  ) async {
    await viewFile(
      context: context,
      selectedFile: state.selectedFile,
      dbImg: dbImg,
      dbImgExt: dbImgExt,
    );
  }

  void deletPickFile(bool dbImage) {
    if (dbImage) {
      emit(state.copyWith(dbFile: false));
    } else {
      emit(state.copyWith(selectedFile: false));
    }
  }

  void submit({
    required BuildContext context,
    required String projectId,
    ActivityModel? editActivity,
  }) async {
    if (state.isLoading) {
      return;
    }
    if (messageController?.text.isEmpty ?? true && state.selectedFile == null) {
      // print(messageController?.text);
      emit(
        state.copyWith(isLoading: false, message: 'Add message or upload file'),
      );
      Navigator.pop(context);
      return;
    }
    emit(state.copyWith(isLoading: true, message: ''));
    //check file is from upload or database
    final fileUrl =
        state.selectedFile != null
            ? await FirebaseStorageRepo().uploadActivityFile(
              state.selectedFile!,
            )
            : state.dbFile;

    final activity = ActivityModel(
      docId: editActivity?.docId ?? '',
      projectId: projectId,
      sendAt: DateTime.now(),
      senderId: FBAuth.auth.currentUser?.uid ?? '',
      message:
          messageController?.text.isEmpty ?? true
              ? null
              : messageController?.text ?? null,
      attachment: fileUrl,
      attachmentType: state.selectedFile?.extension ?? null,
      attachmentName: state.selectedFile?.name ?? '',
    );

    try {
      if (editActivity == null) {
        await activityRepo.createActivity(activity);
        emit(
          state.copyWith(
            isLoading: false,
            message: 'Activity created successfully',
          ),
        );
        Navigator.pop(context);
        return;
      } else {
        await activityRepo.updateActivity(activity);
        emit(
          state.copyWith(
            isLoading: false,
            message: 'Activity updated successfully',
          ),
        );
        Navigator.pop(context);
        return;
      }
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: 'Failed to save activity: ${e.toString()}',
        ),
      );
    }
  }
}
