part of 'activity_form_cubit.dart';

@immutable
class ActivityFormState {
  final String? dbFile;
  final SelectedImage? selectedFile;
  final String message;
  final bool isLoading;
  final String? textmsg;

  ActivityFormState({
    this.dbFile,
    this.selectedFile,
    this.textmsg,
    required this.message,
    required this.isLoading,
  });

  factory ActivityFormState.initial() {
    return ActivityFormState(
      dbFile: null,
      selectedFile: null,
      textmsg: null,
      message: '',
      isLoading: false,
    );
  }

  ActivityFormState copyWith({
    dynamic dbFile,
    dynamic selectedFile,
    String? message,
    bool? isLoading,
    String? textmsg,
  }) {
    return ActivityFormState(
      dbFile: dbFile is bool ? null : (dbFile ?? this.dbFile),
      selectedFile:
          selectedFile is bool ? null : (selectedFile ?? this.selectedFile),
      textmsg: textmsg ?? this.textmsg,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
