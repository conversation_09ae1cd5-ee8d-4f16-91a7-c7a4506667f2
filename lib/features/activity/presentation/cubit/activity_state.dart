part of 'activity_cubit.dart';

@immutable
class ActivityState {
  final List<ActivityModel> activities;
  final String message;
  final bool isLoading;

  ActivityState({
    required this.activities,
    required this.message,
    required this.isLoading,
  });

  factory ActivityState.initial() {
    return ActivityState(activities: [], message: '', isLoading: false);
  }

  ActivityState copyWith({
    List<ActivityModel>? activities,
    String? message,
    bool? isLoading,
  }) {
    return ActivityState(
      activities: activities ?? this.activities,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
