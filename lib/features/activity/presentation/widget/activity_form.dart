import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/activity/domain/antity/activity_model.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_form_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ActivityForm extends StatefulWidget {
  final String projectId;
  final ActivityModel? editActivity;
  const ActivityForm({super.key, required this.projectId, this.editActivity});

  @override
  State<ActivityForm> createState() => _ActivityFormState();
}

class _ActivityFormState extends State<ActivityForm> {
  @override
  void initState() {
    context.read<ActivityFormCubit>().initializeForm(widget.editActivity);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ActivityFormCubit, ActivityFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        final activityFormCubit = context.read<ActivityFormCubit>();
        return IgnorePointer(
          ignoring: state.isLoading,
          child: Form(
            key: context.read<ActivityFormCubit>().formKey,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Text("Activity", style: AppTextStyles.formtitle),
                        Spacer(),
                        IconButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          icon: Icon(CupertinoIcons.xmark),
                        ),
                      ],
                    ),
                    SizedBox(height: 20),

                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              activityFormCubit.pickFile(context);
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                vertical: 60,
                                horizontal: 10,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.attach_file),
                                  Text("Upload Document "),
                                ],
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 20),
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              activityFormCubit.pickFileFromCamera(context);
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                vertical: 60,
                                horizontal: 10,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,

                                children: [
                                  Icon(CupertinoIcons.camera),
                                  Text("Take New Photo"),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 30),
                    buildFilePreview(
                      context: context,
                      selectedFile: state.selectedFile,
                      dbFile: state.dbFile,
                      dbFileExt: widget.editActivity?.attachmentType,
                      dbFileName: widget.editActivity?.attachmentName,
                      isEdit: true,
                      onDelete: () {
                        final isDbFile =
                            state.selectedFile == null && state.dbFile != null;
                        context.read<ActivityFormCubit>().deletPickFile(
                          isDbFile,
                        );
                      },
                      onView: () {
                        context.read<ActivityFormCubit>().viewPickFile(
                          state.dbFile,
                          context,
                          widget.editActivity?.attachmentType,
                        );
                      },
                      isMessage: false,
                    ),
                    SizedBox(height: 10),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                          child: TextFormField(
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Enter Message';
                              }
                              return null;
                            },
                            controller: activityFormCubit.messageController,
                            decoration: InputDecoration(
                              suffixStyle: TextStyle(
                                background:
                                    Paint()..color = AppColors.secondary,
                              ),
                              suffixIcon: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: InkWell(
                                  onTap: () {
                                    context.read<ActivityFormCubit>().submit(
                                      context: context,
                                      projectId: widget.projectId,
                                      editActivity: null,
                                    );
                                  },
                                  child: Container(
                                    padding: EdgeInsets.all(10),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: AppColors.secondary,
                                    ),
                                    child:
                                        state.isLoading
                                            ? Container(
                                              width: 20,
                                              height: 20,
                                              child: CircularProgressIndicator(
                                                valueColor:
                                                    AlwaysStoppedAnimation<
                                                      Color
                                                    >(Colors.white),
                                              ),
                                            )
                                            : Icon(
                                              Icons.send,
                                              color: Colors.white,
                                            ),
                                  ),
                                ),
                              ),
                              hintText: "Enter Message",
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 30),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
