import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/admin/domain/entity/admin_task_model.dart';
import 'package:cp_associates/features/admin/domain/repo/admin_repo.dart';

class FirebaseAdminTaskRepo implements AdminTaskRepo {
  final adminTaskRef = FBFireStore.adminTasks;

  @override
  Future<void> createAdminTask(AdminTaskModel adminTask) async {
    final docRef = adminTaskRef.doc();
    final newAdminTask = adminTask.copyWith(docId: docRef.id);
    await docRef.set(newAdminTask.toJson());
  }

  @override
  Future<void> updateAdminTask(AdminTaskModel adminTask) async {
    await adminTaskRef.doc(adminTask.docId).update(adminTask.toJson());
  }

  @override
  Future<void> deleteAdminTask(String adminTaskId) async {
    await adminTaskRef.doc(adminTaskId).delete();
  }

  @override
  Stream<List<AdminTaskModel>> getAllAdminTask() {
    return adminTaskRef.where('isCompleted', isEqualTo: false).snapshots().map((
      snapshot,
    ) {
      return snapshot.docs
          .map((doc) => AdminTaskModel.fromSnapshot(doc))
          .toList();
    });
  }

  @override
  Stream<List<AdminTaskModel>> getCompletedTask(DateTime month) {
    final startOfMonth = DateTime(month.year, month.month);
    final endOfMonth = DateTime(month.year, month.month + 1);

    return adminTaskRef
        .where('isCompleted', isEqualTo: true)
        .where('completedAt', isGreaterThanOrEqualTo: startOfMonth)
        .where('completedAt', isLessThan: endOfMonth)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => AdminTaskModel.fromSnapshot(doc))
              .toList();
        });
  }
}
