import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/features/admin/domain/entity/admin_task_model.dart';
import 'package:cp_associates/features/admin/domain/repo/admin_repo.dart';
import 'package:meta/meta.dart';

part 'admin_task_state.dart';

class AdminTaskCubit extends Cubit<AdminTaskState> {
  AdminTaskCubit(this.adminTaskRepo) : super(AdminTaskState.initial());

  AdminTaskRepo adminTaskRepo;
  StreamSubscription<List<AdminTaskModel>>? adminTaskStream;

  void fetchAllAdminTask() {
    print("AdminTaskStream------");
    emit(state.copyWith(isLoading: true, message: ""));

    adminTaskStream?.cancel();

    adminTaskStream = adminTaskRepo.getAllAdminTask().listen(
      (adminTask) {
        print("AdminTaskStream------2");
        emit(
          state.copyWith(adminTasks: adminTask, isLoading: false, message: ""),
        );
      },
      onError: (error) {
        print(error.toString());
        emit(
          state.copyWith(
            isLoading: false,
            message: "Failed to fetch admin task: ${error.toString()}",
          ),
        );
      },
    );
  }

  void fetchCompletedAdminTask(DateTime month) {
    print("AdminTaskCompletedStream------");
    emit(state.copyWith(isLoading: true, message: ""));

    adminTaskStream?.cancel();

    adminTaskStream = adminTaskRepo
        .getCompletedTask(month)
        .listen(
          (adminTask) {
            print("AdminTaskCompletedStream------2");
            emit(
              state.copyWith(
                adminTasks: adminTask,
                isLoading: false,
                message: "",
              ),
            );
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch admin task: ${error.toString()}",
              ),
            );
          },
        );
  }

  void updateAdminTask(AdminTaskModel adminTask) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await adminTaskRepo.updateAdminTask(adminTask);
      emit(
        state.copyWith(
          isLoading: false,
          message: "Admin Task updated successfully",
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to update admin task: ${e.toString()}",
        ),
      );
    }
  }

  void deleteAdminTask(String adminTaskId) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await adminTaskRepo.deleteAdminTask(adminTaskId);
      emit(
        state.copyWith(
          isLoading: false,
          message: "Admin Task deleted successfully",
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to delete admin task: ${e.toString()}",
        ),
      );
    }
  }

  void fetchTaskById(String adminTaskId) async {
    emit(state.copyWith(isLoading: true));
    final adminTask = state.adminTasks.firstWhere(
      (task) => task.docId == adminTaskId,
    );
    emit(state.copyWith(isLoading: false, adminTaskDetail: adminTask));
  }

  void filterTask(String type) {
    emit(state.copyWith(isLoading: true, selectedType: type, message: ""));

    if (type == AdminTaskTypes.onGoing) {
      fetchAllAdminTask();
      final tasks =
          state.adminTasks.where((task) => task.isCompleted == false).toList();
      emit(
        state.copyWith(
          filteredAdminTasks: tasks,
          isLoading: false,
          message: "",
          selectedType: AdminTaskTypes.onGoing,
        ),
      );
    } else if (type == AdminTaskTypes.completed) {
      final now = DateTime.now();
      emit(state.copyWith(selectedMonth: now.month));
      fetchCompletedAdminTask(DateTime(now.year, now.month));
      emit(
        state.copyWith(
          isLoading: false,
          message: "",
          selectedType: AdminTaskTypes.completed,
        ),
      );
    }
  }

  void selectMonth(int value) {
    emit(state.copyWith(selectedMonth: value));
  }

  void getMontlhyTask(DateTime month) {
    emit(state.copyWith(isLoading: true, message: ""));
    if (state.adminTasks.isEmpty) {
      emit(state.copyWith(isLoading: false, message: "No task found"));
      return;
    }
    final tasks =
        state.adminTasks
            .where(
              (task) =>
                  task.createdAt.year == month.year &&
                  task.createdAt.month == month.month,
            )
            .toList();
    emit(
      state.copyWith(filteredAdminTasks: tasks, isLoading: false, message: ""),
    );
  }

  @override
  Future<void> close() {
    adminTaskStream?.cancel();
    return super.close();
  }
}
