import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/admin/domain/entity/admin_task_model.dart';
import 'package:cp_associates/features/admin/domain/repo/admin_repo.dart';
import 'package:flutter/material.dart';

part 'admin_task_form_state.dart';

class AdminTaskFormCubit extends Cubit<AdminTaskFormState> {
  AdminTaskRepo adminTaskRepo;
  AdminTaskFormCubit(this.adminTaskRepo) : super(AdminTaskFormState.initial());

  final titleController = TextEditingController();
  final descController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  void initializeForm(AdminTaskModel? editAdminTask) {
    if (editAdminTask != null) {
      titleController.text = editAdminTask.title;
      descController.text = editAdminTask.desc;
      emit(state.copyWith(selectedProject: editAdminTask.projectId));
    } else {
      emit(AdminTaskFormState.initial());
    }
  }

  void selectProject(String projectId) {
    emit(state.copyWith(selectedProject: projectId));
  }

  void submit(AdminTaskModel? editAdminTask, BuildContext context) async {
    if (state.isLoading) {
      return;
    }
    if (formKey.currentState?.validate() ?? false) {
      emit(state.copyWith(isLoading: true, message: ''));
      try {
        final adminTask = AdminTaskModel(
          docId: editAdminTask?.docId ?? "",
          projectId: state.selectedProject,
          createdAt: editAdminTask?.createdAt ?? DateTime.now(),
          createdBy: FBAuth.auth.currentUser?.uid ?? "",
          title: titleController.text,
          desc: descController.text,
          isCompleted: editAdminTask?.isCompleted ?? false,
        );

        if (editAdminTask == null) {
          await adminTaskRepo.createAdminTask(adminTask);
          emit(
            state.copyWith(
              isLoading: false,
              message: "New Admin Task created successfully",
            ),
          );
          Navigator.of(context).pop();
          
        } else {
          await adminTaskRepo.updateAdminTask(adminTask);
          emit(
            state.copyWith(
              isLoading: false,
              message: "Update Admin Task successfully",
            ),
          );
          Navigator.of(context).pop();
        }
      } catch (e) {
        emit(state.copyWith(isLoading: false, message: e.toString()));
      }
    }
  }
}
