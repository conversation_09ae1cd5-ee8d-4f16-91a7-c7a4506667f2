part of 'admin_task_form_cubit.dart';

@immutable
class AdminTaskFormState {
  final String message;
  final bool isLoading;
  final String? selectedProject;

  AdminTaskFormState({
    required this.message,
    required this.isLoading,
    required this.selectedProject,
  });

  factory AdminTaskFormState.initial() {
    return AdminTaskFormState(
      message: '',
      isLoading: false,
      selectedProject: null,
    );
  }

  AdminTaskFormState copyWith({
    String? message,
    bool? isLoading,
    String? selectedProject,
  }) {
    return AdminTaskFormState(
      selectedProject: selectedProject ?? this.selectedProject,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
