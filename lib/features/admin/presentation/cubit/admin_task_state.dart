part of 'admin_task_cubit.dart';

@immutable
class AdminTaskState {
  final List<AdminTaskModel> adminTasks;
  final List<AdminTaskModel> filteredAdminTasks;
  final AdminTaskModel? adminTaskDetail;
  final String selectedType;
  final int selectedMonth;

  final String message;
  final bool isLoading;

  AdminTaskState({
    required this.adminTasks,
    required this.filteredAdminTasks,
    required this.adminTaskDetail,
    required this.selectedType,
    required this.selectedMonth,

    required this.message,

    required this.isLoading,
  });

  factory AdminTaskState.initial() {
    return AdminTaskState(
      adminTasks: [],
      filteredAdminTasks: [],
      adminTaskDetail: null,
      selectedType: 'Ongoing',
      selectedMonth: DateTime.now().month,
      message: '',
      isLoading: false,
    );
  }

  AdminTaskState copyWith({
    List<AdminTaskModel>? adminTasks,
    List<AdminTaskModel>? filteredAdminTasks,
    AdminTaskModel? adminTaskDetail,
    String? selectedType,
    int? selectedMonth,
    String? message,
    bool? isLoading,
  }) {
    return AdminTaskState(
      adminTasks: adminTasks ?? this.adminTasks,
      filteredAdminTasks: filteredAdminTasks ?? this.filteredAdminTasks,
      adminTaskDetail: adminTaskDetail ?? this.adminTaskDetail,
      selectedType: selectedType ?? this.selectedType,
      selectedMonth: selectedMonth ?? this.selectedMonth,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
