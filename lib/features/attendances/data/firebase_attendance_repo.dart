import 'package:cp_associates/features/attendances/domain/antity/request_model.dart';
import 'package:cp_associates/features/attendances/domain/repo/attendance_repo.dart';
import 'package:cp_associates/features/attendances/domain/antity/punches_model.dart';
import 'package:cp_associates/features/attendances/domain/antity/records_model.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class FirebaseAttendanceRepo implements AttendanceRepo {
  final punchesRef = FBFireStore.punches;
  final recordsRef = FBFireStore.records;

  //user
  // Future<PunchesModel?> getLastPunch(String userId) async {
  //   final lastPunchQuery =
  //       await punchesRef
  //           .where('userId', isEqualTo: userId)
  //           .orderBy('createdAt', descending: true)
  //           .limit(1)
  //           .get();

  //   if (lastPunchQuery.docs.isNotEmpty) {
  //     return PunchesModel.fromSnapshot(lastPunchQuery.docs.first);
  //   }
  //   return null;
  // }

  @override
  Stream<PunchesModel?> listenToLastPunch(String userId) {
    return punchesRef
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .limit(1)
        .snapshots()
        .map((snapshot) {
          if (snapshot.docs.isEmpty) return null;
          return PunchesModel.fromSnapshot(snapshot.docs.first);
        });
  }

  @override
  Future<void> punchIn(String userId) async {
    final now = DateTime.now();
    final todayStart = DateTime(now.year, now.month, now.day);
    final monthStart = DateTime(now.year, now.month);

    // 1. Save punch-in record
    final punchDoc = punchesRef.doc();
    final punch = PunchesModel(
      docId: punchDoc.id,
      userId: userId,
      punchIn: true,
      createdAt: now,
    );
    await punchDoc.set(punch.toJson());

    // 2. Get or create monthly record
    final recordQuery =
        await recordsRef
            .where('userId', isEqualTo: userId)
            .where('createdAt', isGreaterThanOrEqualTo: monthStart)
            .where('createdAt', isLessThan: DateTime(now.year, now.month + 1))
            .limit(1)
            .get();

    RecordsModel? record;
    DocumentReference? recordDocRef;

    if (recordQuery.docs.isNotEmpty) {
      record = RecordsModel.fromSnapshot(recordQuery.docs.first);
      recordDocRef = recordsRef.doc(record.docId);
    } else {
      // Create new monthly record
      final newRecord = RecordsModel(
        docId: '',
        userId: userId,
        createdAt: now,
        lastAttend: now,
        totalAttendance: 0,
        totalMinutes: 0,
      );
      final newDoc = await recordsRef.add(newRecord.toJson());
      recordDocRef = newDoc;
      record = newRecord.copyWith(docId: newDoc.id);
    }

    // 3. Check if this is the first punch-in today
    final todayPunchQuery =
        await punchesRef
            .where('userId', isEqualTo: userId)
            .where('punchIn', isEqualTo: true)
            .where('createdAt', isGreaterThanOrEqualTo: todayStart)
            .where('createdAt', isLessThan: todayStart.add(Duration(days: 1)))
            .get();

    // Since we just added one punch-in, length == 1 means first punch-in of the day
    final alreadyAttendedToday = todayPunchQuery.docs.length > 1;

    if (!alreadyAttendedToday) {
      // Increment attendance count
      await recordDocRef.update({
        'totalAttendance': record.totalAttendance + 1,
        'lastAttend': now,
      });
    }
  }

  @override
  Future<void> punchOut(String userId, DateTime reqTime) async {
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month);

    final lastPunchQuery =
        await punchesRef
            .where('userId', isEqualTo: userId)
            .orderBy('createdAt', descending: true)
            .limit(1)
            .get();

    if (lastPunchQuery.docs.isEmpty) return; // No punch found

    // 1. Save punch-out record
    final punchOutDoc = punchesRef.doc();
    final punchOut = PunchesModel(
      docId: punchOutDoc.id,
      userId: userId,
      punchIn: false,
      createdAt: reqTime,
    );
    await punchOutDoc.set(punchOut.toJson());

    // 2. Find the last punch-in to calculate work duration
    final lastPunchInQuery =
        await punchesRef
            .where('userId', isEqualTo: userId)
            .where('punchIn', isEqualTo: true)
            .orderBy('createdAt', descending: true)
            .limit(1)
            .get();

    if (lastPunchInQuery.docs.isEmpty) return; // No punch-in found

    final lastPunchIn = PunchesModel.fromSnapshot(lastPunchInQuery.docs.first);
    final minutesWorked = reqTime.difference(lastPunchIn.createdAt).inMinutes;
    if (minutesWorked <= 0) return; // Invalid duration

    // 3. Get monthly record
    final recordQuery =
        await recordsRef
            .where('userId', isEqualTo: userId)
            .where('createdAt', isGreaterThanOrEqualTo: monthStart)
            .where('createdAt', isLessThan: DateTime(now.year, now.month + 1))
            .limit(1)
            .get();

    if (recordQuery.docs.isEmpty)
      return; // Monthly record missing (should not happen)

    final record = RecordsModel.fromSnapshot(recordQuery.docs.first);

    // 4. Update totalMinutes and lastAttend
    await recordsRef.doc(record.docId).update({
      'totalMinutes': record.totalMinutes + minutesWorked,
      'lastAttend': reqTime,
    });
  }

  @override
  Stream<List<PunchesModel>> fetchUserDayPunches(String userId, DateTime day) {
    return punchesRef
        .where('userId', isEqualTo: userId)
        .where('createdAt', isGreaterThanOrEqualTo: day)
        .where('createdAt', isLessThan: day.add(Duration(days: 1)))
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => PunchesModel.fromSnapshot(doc))
              .toList();
        });
  }

  @override
  Future<void> requestPunchOutAfter7pm(String userId, DateTime reqTime) async {
    final now = DateTime.now();
    final requestRef = FBFireStore.requests;
    final requestDoc = requestRef.doc();
    final request = RequestModel(
      docId: requestDoc.id,
      uId: userId,
      createdAt: now,
      reqTime: reqTime,
      active: true,
    );
    await requestDoc.set(request.toJson());
  }

  @override
  Stream<bool> listenToActiveRequestStatus(String userId) {
    return FBFireStore.requests.where('uId', isEqualTo: userId).snapshots().map(
      (snapshot) {
        return snapshot.docs.any((doc) => doc['active'] == true);
      },
    );
  }

  //admin
  @override
  Stream<List<PunchesModel>> fetchAllUserToadyPunchesRecord() {
    return punchesRef
        .where(
          'createdAt',
          isGreaterThanOrEqualTo: DateTime(
            DateTime.now().year,
            DateTime.now().month,
            DateTime.now().day,
          ),
        )
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => PunchesModel.fromSnapshot(doc))
              .toList();
        });
  }

  @override
  Stream<RecordsModel> fetchUserMonthlyRecords(String userId, DateTime month) {
    return recordsRef
        .where('userId', isEqualTo: userId)
        .where('createdAt', isGreaterThanOrEqualTo: month)
        .where('createdAt', isLessThan: DateTime(month.year, month.month + 1))
        .snapshots()
        .map((snapshot) {
          return RecordsModel.fromSnapshot(snapshot.docs.first);
        });
  }

  @override
  Stream<List<RequestModel>> fetchActiveRequests() {
    return FBFireStore.requests
        .where('active', isEqualTo: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => RequestModel.fromSnapshot(doc))
              .toList();
        });
  }

  @override
  Future<void> approveRequestAndPunchOut(
    RequestModel request,
    DateTime newReqTime,
  ) async {
    await FBFireStore.requests.doc(request.docId).update({
      'active': false,
      'reqTime': newReqTime,
    });
    print("-----1");
    await punchOut(request.uId, newReqTime);
    print("-----2");
  }
}
