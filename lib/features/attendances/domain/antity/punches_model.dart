import 'package:cloud_firestore/cloud_firestore.dart';

class PunchesModel {
  final String docId;
  final String userId;
  final bool punchIn;
  final DateTime createdAt;

  PunchesModel({
    required this.docId,
    required this.userId,
    required this.punchIn,
    required this.createdAt,
  });

  PunchesModel copyWith({
    String? docId,
    String? userId,
    bool? punchIn,
    DateTime? createdAt,
  }) {
    return PunchesModel(
      docId: docId ?? this.docId,
      userId: userId ?? this.userId,
      punchIn: punchIn ?? this.punchIn,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {'userId': userId, 'punchIn': punchIn, 'createdAt': createdAt};
  }

  factory PunchesModel.fromMap(Map<String, dynamic> map) {
    return PunchesModel(
      docId: map['docId'] ?? '',
      userId: map['userId'] ?? '',
      punchIn: map['punchIn'] ?? false,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
    );
  }
  factory PunchesModel.fromJson(Map<String, dynamic> json) {
    return PunchesModel(
      docId: json['docId'] ?? '',
      userId: json['userId'] ?? '',
      punchIn: json['punchIn'] ?? false,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
    );
  }
  Map<String, dynamic> toJson() {
    return {'userId': userId, 'punchIn': punchIn, 'createdAt': createdAt};
  }

  factory PunchesModel.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return PunchesModel.fromJson({
      ...data,
      'docId': snapshot.id, // override docId from document ID
    });
  }
}
