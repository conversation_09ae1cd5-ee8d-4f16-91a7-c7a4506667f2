import 'package:cloud_firestore/cloud_firestore.dart';

class RecordsModel {
  final String docId;
  final String userId;
  final DateTime createdAt;
  final DateTime lastAttend;
  final num totalAttendance;
  final num totalMinutes;

  RecordsModel({
    required this.docId,
    required this.userId,
    required this.createdAt,
    required this.lastAttend,
    required this.totalAttendance,
    required this.totalMinutes,
  });

  RecordsModel copyWith({
    String? docId,
    String? userId,
    DateTime? createdAt,
    DateTime? lastAttend,
    num? totalAttendance,
    num? totalMinutes,
  }) {
    return RecordsModel(
      docId: docId ?? this.docId,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      lastAttend: lastAttend ?? this.lastAttend,
      totalAttendance: totalAttendance ?? this.totalAttendance,
      totalMinutes: totalMinutes ?? this.totalMinutes,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'createdAt': createdAt,
      'lastAttend': lastAttend,
      'totalAttendance': totalAttendance,
      'totalMinutes': totalMinutes,
    };
  }

  factory RecordsModel.fromMap(Map<String, dynamic> map) {
    return RecordsModel(
      docId: map['docId'] ?? '',
      userId: map['userId'] ?? '',
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      lastAttend: (map['lastAttend'] as Timestamp).toDate(),
      totalAttendance: map['totalAttendance'] ?? 0,
      totalMinutes: map['totalMinutes'] ?? 0,
    );
  }
  factory RecordsModel.fromJson(Map<String, dynamic> json) {
    return RecordsModel(
      docId: json['docId'] ?? '',
      userId: json['userId'] ?? '',
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      lastAttend: (json['lastAttend'] as Timestamp).toDate(),
      totalAttendance: json['totalAttendance'] ?? 0,
      totalMinutes: json['totalMinutes'] ?? 0,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'createdAt': createdAt,
      'lastAttend': lastAttend,
      'totalAttendance': totalAttendance,
      'totalMinutes': totalMinutes,
    };
  }

  factory RecordsModel.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return RecordsModel.fromJson({
      ...data,
      'docId': snapshot.id, // override docId from document ID
    });
  }
}
