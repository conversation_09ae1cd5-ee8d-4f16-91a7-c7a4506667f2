import 'package:cloud_firestore/cloud_firestore.dart';

class RequestModel {
  final String docId;
  final String uId;
  final DateTime createdAt;
  final DateTime reqTime;
  final bool active;

  RequestModel({
    required this.docId,
    required this.uId,
    required this.createdAt,
    required this.active,
    required this.reqTime,
  });

  RequestModel copyWith({
    String? docId,
    String? uId,
    DateTime? createdAt,
    DateTime? reqTime,
    bool? active,
  }) {
    return RequestModel(
      docId: docId ?? this.docId,
      uId: uId ?? this.uId,
      createdAt: createdAt ?? this.createdAt,
      reqTime: reqTime ?? this.reqTime,
      active: active ?? this.active,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'docId': docId,
      'uId': uId,
      'createdAt': createdAt,
      'reqTime': reqTime,
      'active': active,
    };
  }

  factory RequestModel.fromJson(Map<String, dynamic> json) {
    return RequestModel(
      docId: json['docId'] ?? '',
      uId: json['uId'] ?? '',
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      reqTime: (json['reqTime'] as Timestamp).toDate(),
      active: json['active'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'uId': uId,
      'createdAt': createdAt,
      'reqTime': reqTime,
      'active': active,
    };
  }

  factory RequestModel.fromSnapshot(
    QueryDocumentSnapshot<Map<String, dynamic>> snap,
  ) {
    return RequestModel(
      docId: snap.id,
      uId: snap['uId'],
      createdAt: (snap['createdAt'] as Timestamp).toDate(),
      reqTime: (snap['reqTime'] as Timestamp).toDate(),
      active: snap['active'],
    );
  }

  // Method to convert an instance to a snapshot
  Map<String, dynamic> toSnap() {
    return {
      'docId': docId,
      'uId': uId,
      'createdAt': Timestamp.fromDate(createdAt),
      'reqTime': reqTime,
      'active': active,
    };
  }
}
