import 'package:cp_associates/features/attendances/domain/antity/punches_model.dart';
import 'package:cp_associates/features/attendances/domain/antity/records_model.dart';
import 'package:cp_associates/features/attendances/domain/antity/request_model.dart';

abstract class AttendanceRepo {
  //user
  Future<void> punchIn(String userId);
  Future<void> punchOut(String userId, DateTime reqTime);

  //user
  Stream<List<PunchesModel>> fetchUserDayPunches(String userId, DateTime day);
  // Future<PunchesModel?> getLastPunch(String userId);
  Stream<PunchesModel?> listenToLastPunch(String userId);
  Future<void> requestPunchOutAfter7pm(String userId, DateTime reqTime);
  Stream<bool> listenToActiveRequestStatus(String userId);

  // admin
  Stream<List<PunchesModel>> fetchAllUserToadyPunchesRecord();
  Stream<List<RequestModel>> fetchActiveRequests();
  Stream<RecordsModel> fetchUserMonthlyRecords(String userId, DateTime month);
  Future<void> approveRequestAndPunchOut(
    RequestModel request,
    DateTime newReqTime,
  );
}
