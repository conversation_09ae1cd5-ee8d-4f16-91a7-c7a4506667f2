import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/features/attendances/data/firebase_attendance_repo.dart';
import 'package:cp_associates/features/attendances/domain/antity/punches_model.dart';
import 'package:cp_associates/features/attendances/domain/antity/records_model.dart';
import 'package:cp_associates/features/attendances/domain/antity/request_model.dart';
import 'package:flutter/material.dart';

part 'attendance_admin_state.dart';

class AttendanceAdminCubit extends Cubit<AttendanceAdminState> {
  FirebaseAttendanceRepo attendanceRepo;
  AttendanceAdminCubit(this.attendanceRepo)
    : super(AttendanceAdminState.initial());

  StreamSubscription<List<RequestModel>>? requestsStream;
  StreamSubscription<List<PunchesModel>>? punchesStream;
  StreamSubscription<RecordsModel>? recordsStream;

  void fetchAllUserToadyPunchesRecord() {
    print("PunchesStream------");
    emit(state.copyWith(isLoading: true, message: ""));

    punchesStream?.cancel();

    punchesStream = attendanceRepo.fetchAllUserToadyPunchesRecord().listen(
      (punches) {
        print("PunchesStream------2");
        emit(state.copyWith(punches: punches, isLoading: false, message: ""));
      },
      onError: (error) {
        print(error.toString());
        emit(
          state.copyWith(
            isLoading: false,
            message: "Failed to fetch punches: ${error.toString()}",
          ),
        );
      },
    );
  }

  void fetchActiveRequests() {
    print("RequestsStream------");
    emit(state.copyWith(isLoading: true, message: ""));

    requestsStream?.cancel();

    requestsStream = attendanceRepo.fetchActiveRequests().listen(
      (requests) {
        print("RequestsStream------2");
        emit(state.copyWith(requests: requests, isLoading: false, message: ""));
      },
      onError: (error) {
        print(error.toString());
        emit(
          state.copyWith(
            isLoading: false,
            message: "Failed to fetch requests: ${error.toString()}",
          ),
        );
      },
    );
  }

  void fetchUserMonthlyRecords(String userId, DateTime month) {
    print("RecordsStream------");
    emit(state.copyWith(isLoading: true, message: ""));

    recordsStream?.cancel();

    recordsStream = attendanceRepo
        .fetchUserMonthlyRecords(userId, month)
        .listen(
          (record) {
            print("RecordsStream------2");
            emit(
              state.copyWith(records: [record], isLoading: false, message: ""),
            );
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch records: ${error.toString()}",
              ),
            );
          },
        );
  }

  void approveRequest(RequestModel request, DateTime updatedTime) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await attendanceRepo.approveRequestAndPunchOut(request, updatedTime);
      emit(state.copyWith(isLoading: false, message: "Request approved"));
    } catch (e) {
      emit(
        state.copyWith(isLoading: false, message: "Failed to approve request"),
      );
    }
  }

  Future<DateTime?> selectDateTime(
    BuildContext context,
    DateTime initialDateTime,
  ) async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDateTime,
      firstDate: DateTime(2022),
      lastDate: DateTime(2100),
    );

    if (pickedDate == null) return null;

    final pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(initialDateTime),
    );

    if (pickedTime == null) return null;

    return DateTime(
      pickedDate.year,
      pickedDate.month,
      pickedDate.day,
      pickedTime.hour,
      pickedTime.minute,
    );
  }

  @override
  Future<void> close() {
    requestsStream?.cancel();
    punchesStream?.cancel();
    recordsStream?.cancel();
    return super.close();
  }
}
