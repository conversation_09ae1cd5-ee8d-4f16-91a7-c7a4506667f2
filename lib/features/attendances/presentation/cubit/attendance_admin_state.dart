part of 'attendance_admin_cubit.dart';

class AttendanceAdminState {
  final String message;
  final bool isLoading;
  final List<PunchesModel> punches;
  final List<RecordsModel> records;
  final List<RequestModel> requests;

  AttendanceAdminState({
    required this.message,
    required this.isLoading,
    required this.punches,
    required this.records,
    required this.requests,
  });

  factory AttendanceAdminState.initial() {
    return AttendanceAdminState(
      message: '',
      isLoading: false,
      punches: [],
      records: [],
      requests: [],
    );
  }

  AttendanceAdminState copyWith({
    String? message,
    bool? isLoading,
    List<PunchesModel>? punches,
    List<RecordsModel>? records,
    List<RequestModel>? requests,
  }) {
    return AttendanceAdminState(
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      punches: punches ?? this.punches,
      records: records ?? this.records,
      requests: requests ?? this.requests,
    );
  }
}
