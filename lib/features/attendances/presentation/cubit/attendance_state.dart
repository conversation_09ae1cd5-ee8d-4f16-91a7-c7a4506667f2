part of 'attendance_cubit.dart';

@immutable
class AttendanceState {
  final String message;
  final bool isLoading;
  final List<PunchesModel> punches;
  final PunchesModel? lastPunch;
  final bool isPunchedIn;
  final bool showRequestPunchOut;
  final String selectedFilter;
  final List<PunchesModel> filteredPunches;
  final DateTime selectedDate;
  final RecordsModel? currentRecord;
  final int selectedMonth;
  final bool hasActiveRequest;

  const AttendanceState({
    required this.message,
    required this.isLoading,
    required this.punches,
    required this.lastPunch,
    required this.isPunchedIn,
    required this.showRequestPunchOut,
    required this.selectedFilter,
    required this.filteredPunches,
    required this.selectedDate,
    required this.currentRecord,
    required this.selectedMonth,
    required this.hasActiveRequest,
  });

  factory AttendanceState.initial() {
    return AttendanceState(
      message: '',
      isLoading: false,
      punches: [],
      lastPunch: null,
      filteredPunches: [],
      isPunchedIn: false,
      showRequestPunchOut: false,
      currentRecord: null,
      selectedDate: DateTime.now(),
      selectedFilter: AttendanceFilter.today,
      selectedMonth: DateTime.now().month,
      hasActiveRequest: false,
    );
  }

  AttendanceState copyWith({
    String? message,
    bool? isLoading,
    List<PunchesModel>? punches,
    PunchesModel? lastPunch,
    bool? isPunchedIn,
    bool? showRequestPunchOut,
    String? selectedFilter,
    List<PunchesModel>? filteredPunches,
    DateTime? selectedDate,
    RecordsModel? currentRecord,
    int? selectedMonth,
    List<RequestModel>? requests,
    RequestModel? request,
    bool? hasActiveRequest,
  }) {
    return AttendanceState(
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      punches: punches ?? this.punches,
      lastPunch: lastPunch ?? this.lastPunch,
      isPunchedIn: isPunchedIn ?? this.isPunchedIn,
      showRequestPunchOut: showRequestPunchOut ?? this.showRequestPunchOut,
      selectedFilter: selectedFilter ?? this.selectedFilter,
      filteredPunches: filteredPunches ?? this.filteredPunches,
      selectedDate: selectedDate ?? this.selectedDate,
      currentRecord: currentRecord ?? this.currentRecord,
      selectedMonth: selectedMonth ?? this.selectedMonth,
      hasActiveRequest: hasActiveRequest ?? this.hasActiveRequest,
    );
  }
}
