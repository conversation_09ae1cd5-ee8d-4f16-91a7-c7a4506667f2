import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/features/attendances/presentation/widget/request_tile.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class RequestDialog extends StatelessWidget {
  const RequestDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
        width: 500,
        height: 500,

        child: Column(
          children: [
            Row(
              children: [
                Text("Requests", style: AppTextStyles.appBarHeading),
                Spacer(),
                IconButton(
                  onPressed: () {
                    context.pop();
                  },
                  icon: Icon(Icons.close, size: 30),
                ),
              ],
            ),

            SizedBox(height: 20),

            RequestTile(),
          ],
        ),
      ),
    );
  }
}
