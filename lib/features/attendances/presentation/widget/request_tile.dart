import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RequestTile extends StatelessWidget {
  const RequestTile({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AttendanceAdminCubit, AttendanceAdminState>(
      builder: (context, state) {
        if (state.requests.isEmpty) {
          return Center(child: Text("No Request Avaliable"));
        }
        if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          child: Column(
            children: [
              ...List.generate(state.requests.length, (index) {
                final request = state.requests[index];
                final user = context.read<UserCubit>().getUserById(request.uId);

                final statusText = request.active ? "Active" : "Approved";
                final statusColor =
                    request.active ? Colors.orange : Colors.green;

                return TransparentInkWell(
                  onTap: () async {
                    final selectedDateTime = await context
                        .read<AttendanceAdminCubit>()
                        .selectDateTime(context, request.reqTime);
                    if (selectedDateTime != null) {
                      context.read<AttendanceAdminCubit>().approveRequest(
                        request,
                        selectedDateTime,
                      );
                    }
                  },

                  child:
                      true
                          ? Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: AppColors.containerGreyColor,
                              border: Border.all(color: AppColors.borderGrey),
                              borderRadius: BorderRadius.circular(12),
                            ),

                            child: Row(
                              children: [
                                Container(
                                  height: 60,
                                  width: 60,
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: getColorFromInput(user?.name ?? "?"),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Center(
                                    child: Text(
                                      (user?.name ?? "?")[0].toUpperCase(),
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 20,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 20),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "${user?.name ?? ""}  (${user?.role ?? ""})",
                                    ),
                                    // Text(user?.email ?? ""),
                                    Text(
                                      "Requested At: ${request.createdAt.goodTime()}",
                                    ),
                                    Text(
                                      "Requested Time: ${request.reqTime.goodTime()}",
                                    ),
                                  ],
                                ),
                                Spacer(),
                                Icon(
                                  CupertinoIcons.check_mark_circled,
                                  size: 25,
                                  color: AppColors.primary,
                                ),
                              ],
                            ),
                          )
                          : Card(
                            color: AppColors.containerGreyColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: ListTile(
                              contentPadding: const EdgeInsets.symmetric(
                                vertical: 12,
                                horizontal: 16,
                              ),
                              leading: CircleAvatar(
                                backgroundColor: getColorFromInput(
                                  user?.name ?? "?",
                                ),
                                child: Text(
                                  (user?.name ?? "?")[0].toUpperCase(),
                                ),
                              ),
                              title: Row(
                                children: [
                                  Text(user?.name ?? ""),
                                  const SizedBox(width: 8),
                                  Text(
                                    "(${user?.role ?? ""})",
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 6),
                                  Text(
                                    "Requested At: ${request.createdAt.goodTime()}",
                                  ),
                                  Text(
                                    "Requested Time: ${request.reqTime.goodTime()}",
                                  ),
                                  const SizedBox(height: 6),
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: statusColor.withOpacity(0.2),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Text(
                                          statusText,
                                          style: TextStyle(
                                            color: statusColor,
                                            fontWeight: FontWeight.w600,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              trailing:
                                  request.active
                                      ? IconButton(
                                        icon: Icon(
                                          CupertinoIcons.check_mark_circled,
                                          color: Colors.green,
                                        ),
                                        onPressed: () async {
                                          final selectedDateTime = await context
                                              .read<AttendanceAdminCubit>()
                                              .selectDateTime(
                                                context,
                                                request.reqTime,
                                              );
                                          if (selectedDateTime != null) {
                                            context
                                                .read<AttendanceAdminCubit>()
                                                .approveRequest(
                                                  request,
                                                  selectedDateTime,
                                                );
                                          }
                                        },
                                      )
                                      : null,
                            ),
                          ),
                );
              }),
            ],
          ),
        );
      },
    );
  }
}
