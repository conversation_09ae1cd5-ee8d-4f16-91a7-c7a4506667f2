import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String docId;
  final String name;
  final String email;
  final String role;

  UserModel({
    required this.docId,
    required this.name,
    required this.email,
    required this.role,
  });

  /// Create a User from Firestore snapshot
  factory UserModel.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;
    return UserModel(
      docId: snap.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      role: data['role'] ?? '',
    );
  }

  /// Create a User from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      docId: json['docId'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      role: json['role'] ?? '',
    );
  }

  /// Convert User to Map for Firestore or other storage
  Map<String, dynamic> toMap() {
    return {'name': name, 'email': email, 'role': role};
  }

  /// Convert User to JSON (include docId if needed)
  Map<String, dynamic> toJson() {
    return {'name': name, 'email': email, 'role': role};
  }
}
