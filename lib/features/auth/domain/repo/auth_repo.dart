import 'package:cp_associates/features/auth/domain/entity/user_model.dart';

abstract class AuthRepo {
  Future<UserModel?> loginWithEmailPassword(String email, String password);
  Future<void> registerWithEmailPassword(
    String name,
    String email,
    String password,
    String role,
  );
  Future<void> logout();

  // Stream<UserModel?> currentUserStream();
  Stream<bool> authStateStream();
  Stream<UserModel?> currentUserStream();
  Future<UserModel?> getCurrentUser();

  Future<void> forgetPassword(String email);
  Future<void> updateProfile(String userId, String name, String role);
}
