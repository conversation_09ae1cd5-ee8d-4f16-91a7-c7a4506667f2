import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:cp_associates/features/auth/presentation/widgets/auth_textfield.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  //Controller
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  bool isPasswordVisible = false;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthCubit, AuthState>(
      listener: (context, state) {
        // Handle authentication state changes
        if (state.isAuthenticated) {
          context.go(Routes.home);
        } else if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        ;
        return ResponsiveWid(
          desktop: Scaffold(
            body: Center(
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.containerGreyColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                constraints: const BoxConstraints(
                  maxWidth: 600,
                  maxHeight: 720,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 40),
                      decoration: const BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: Center(
                        child: Image.asset(
                          "assets/images/logo_light.png",
                          width: 153,
                        ),
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Welcome text
                    Text("Welcome back", style: AppTextStyles.heading),

                    const SizedBox(height: 40),

                    // Email TextField
                    Container(
                      width: 430,
                      child: AuthTextField(
                        obscureText: false,
                        controller: emailController,
                        hintText: "Email",
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Password TextField
                    Container(
                      width: 430,
                      child: AuthTextField(
                        onSubmit: () {
                          context.read<AuthCubit>().login(
                            emailController.text,
                            passwordController.text,
                          );
                        },
                        obscureText: isPasswordVisible,
                        controller: passwordController,
                        hintText: "Password",
                        icon: IconButton(
                          onPressed: () {
                            setState(() {
                              isPasswordVisible = !isPasswordVisible;
                            });
                          },
                          icon: Icon(
                            isPasswordVisible
                                ? Icons.visibility_off_outlined
                                : Icons.visibility_outlined,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Forgot password
                    InkWell(
                      onTap: () {
                        context.read<AuthCubit>().forgetPassword(
                          emailController.text,
                        );
                      },
                      child: Container(
                        width: 430,
                        alignment: Alignment.centerLeft,
                        child: Text(
                          "Forgot password?",
                          style: AppTextStyles.hintText.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 30),

                    // Login button
                    PrimaryButton(
                      width: 430,
                      text: "LOGIN",
                      isLoading: state.loading,
                      onPressed: () {
                        context.read<AuthCubit>().login(
                          emailController.text,
                          passwordController.text,
                        );
                      },
                    ),
                    const SizedBox(height: 100),

                    Text.rich(
                      TextSpan(
                        text: "Developed by ",
                        style: TextStyle(
                          color: AppColors.grey2,
                          fontWeight: FontWeight.bold,
                        ),
                        children: [
                          TextSpan(
                            text: "Diwizon.com",
                            style: TextStyle(
                              decoration: TextDecoration.underline,
                              color: AppColors.primary,
                              fontWeight: FontWeight.bold,
                            ),
                            recognizer:
                                TapGestureRecognizer()
                                  ..onTap = () {
                                    // Use the url_launcher package
                                    launchUrl(Uri.parse("https://diwizon.com"));
                                  },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                  ],
                ),
              ),
            ),
          ),
          mobile: Scaffold(
            body: SingleChildScrollView(
              child: Column(
                children: [
                  Container(
                    width: double.maxFinite,
                    height: 247,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [AppColors.primary, AppColors.secondary],
                      ),
                    ),
                    child: Stack(
                      children: [
                        Align(
                          alignment: Alignment.bottomLeft,
                          child: Padding(
                            padding: const EdgeInsets.all(
                              16.0,
                            ), // Optional: spacing from edges
                            child: Image.asset(
                              "assets/images/logo_light.png",
                              width: 154, // smaller width
                              height: 106, // smaller height
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 100),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    alignment: Alignment.topLeft,
                    child: Text(
                      "Welcome back",
                      style: AppTextStyles.heading.copyWith(fontSize: 22),
                    ),
                  ),
                  SizedBox(height: 40),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: AuthTextField(
                      controller: emailController,
                      hintText: "Email",
                      obscureText: false,
                    ),
                  ),
                  SizedBox(height: 20),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: AuthTextField(
                      onSubmit: () {
                        context.read<AuthCubit>().login(
                          emailController.text.trim(),
                          passwordController.text.trim(),
                        );
                      },

                      obscureText: isPasswordVisible,
                      controller: passwordController,
                      hintText: "Password",
                      icon: IconButton(
                        onPressed: () {
                          setState(() {
                            isPasswordVisible = !isPasswordVisible;
                          });
                        },
                        icon: Icon(
                          isPasswordVisible
                              ? Icons.visibility_off_outlined
                              : Icons.visibility_outlined,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 20),
                  InkWell(
                    onTap: () {
                      // print(emailController.text.trim());
                      context.read<AuthCubit>().forgetPassword(
                        emailController.text.trim(),
                      );
                    },
                    child: Container(
                      width: 430,
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      alignment: Alignment.bottomLeft,
                      child: Text(
                        "Forgot password?",
                        style: AppTextStyles.hintText.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 50),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: PrimaryButton(
                      isLoading: state.loading,
                      text: "LOGIN",
                      onPressed: () {
                        context.read<AuthCubit>().login(
                          emailController.text,
                          passwordController.text,
                        );
                      },
                      width: double.maxFinite,
                    ),
                  ),

                  SizedBox(height: 130),

                  Text.rich(
                    TextSpan(
                      text: "Developed by ",
                      style: TextStyle(
                        color: AppColors.grey2,
                        fontWeight: FontWeight.bold,
                      ),
                      children: [
                        TextSpan(
                          text: "Diwizon.com",
                          style: TextStyle(
                            decoration: TextDecoration.underline,
                            color: AppColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                          recognizer:
                              TapGestureRecognizer()
                                ..onTap = () {
                                  // Use the url_launcher package
                                  launchUrl(Uri.parse("https://diwizon.com"));
                                },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // floatingActionButton: Padding(
            //   padding: const EdgeInsets.symmetric(horizontal: 16),
            //   child: PrimaryButton(
            //     text: "LOGIN",
            //     onPressed: () {
            //       context.read<AuthCubit>().login(
            //         emailController.text,
            //         passwordController.text,
            //       );
            //     },
            //     width: double.maxFinite,
            //   ),
            // ),
            // floatingActionButtonLocation:
            //     FloatingActionButtonLocation.centerFloat,
          ),
        );
      },
    );
  }
}
