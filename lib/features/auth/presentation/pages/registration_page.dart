import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:cp_associates/features/auth/presentation/widgets/auth_textfield.dart';
import 'package:flutter/material.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class RegistrationPage extends StatefulWidget {
  const RegistrationPage({super.key});

  @override
  State<RegistrationPage> createState() => _RegistrationPageState();
}

class _RegistrationPageState extends State<RegistrationPage> {
  //Controller
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  bool isPasswordVisible = false;
  String? selectedRole;
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state.message.isNotEmpty && state.isRegistered) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
          Future.delayed(Duration(seconds: 1), () {
            context.go(Routes.home);
          });
        } else if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        return ResponsiveWid(
          desktop: Scaffold(
            body: Center(
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.containerGreyColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                constraints: const BoxConstraints(maxWidth: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 40),
                      decoration: const BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: Center(
                        child: Image.asset(
                          "assets/images/logo_light.png",
                          width: 153,
                        ),
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Welcome text
                    Text("Register a New User", style: AppTextStyles.heading),

                    const SizedBox(height: 40),

                    // Name TextField
                    Container(
                      width: 430,
                      child: AuthTextField(
                        obscureText: false,
                        controller: nameController,
                        hintText: "Name",
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Email TextField
                    Container(
                      width: 430,
                      child: AuthTextField(
                        obscureText: false,
                        controller: emailController,
                        hintText: "Email",
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Password TextField
                    Container(
                      width: 430,
                      child: AuthTextField(
                        obscureText: isPasswordVisible,
                        controller: passwordController,
                        hintText: "Password",
                        icon: IconButton(
                          onPressed: () {
                            setState(() {
                              isPasswordVisible = !isPasswordVisible;
                            });
                          },
                          icon: Icon(
                            isPasswordVisible
                                ? Icons.visibility_off_outlined
                                : Icons.visibility_outlined,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    Container(
                      width: 430,
                      // padding: EdgeInsets.symmetric(horizontal: 16),
                      child: DropdownButtonFormField(
                        decoration: InputDecoration(
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: AppColors.grey2),
                          ),
                          hintText: "Role",
                          hintStyle: AppTextStyles.hintText,

                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: Role.designer,
                            child: Text(Role.designer),
                          ),
                          DropdownMenuItem(
                            value: Role.supervisor,
                            child: Text(Role.supervisor),
                          ),
                        ],
                        onChanged: (value) {
                          selectedRole = value;
                        },
                      ),
                    ),

                    const SizedBox(height: 30),

                    // Login button
                    PrimaryButton(
                      width: 430,
                      text: "REGISTER",
                      onPressed: () {
                        context.read<AuthCubit>().register(
                          emailController.text,
                          passwordController.text,
                          nameController.text,
                          selectedRole ?? "",
                        );
                      },
                      isLoading: state.loading,
                    ),
                    const SizedBox(height: 100),
                  ],
                ),
              ),
            ),
          ),
          mobile: Scaffold(
            appBar: AppBar(
              backgroundColor: AppColors.primary,
              iconTheme: IconThemeData(color: Colors.white),
            ),
            body: SafeArea(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: double.maxFinite,
                      height: 247,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        // gradient: LinearGradient(
                        //   colors: [AppColors.primary, AppColors.secondary],
                        // ),
                      ),
                      child: Stack(
                        children: [
                          Align(
                            alignment: Alignment.bottomLeft,
                            child: Padding(
                              padding: const EdgeInsets.all(
                                16.0,
                              ), // Optional: spacing from edges
                              child: Image.asset(
                                "assets/images/logo_light.png",
                                width: 154, // smaller width
                                height: 106, // smaller height
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // SizedBox(height: 100),
                    SizedBox(height: 40),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      alignment: Alignment.topLeft,
                      child: Text(
                        "Register a New User",
                        style: AppTextStyles.heading.copyWith(fontSize: 22),
                      ),
                    ),
                    SizedBox(height: 40),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: AuthTextField(
                        controller: nameController,
                        hintText: "Name",
                        obscureText: false,
                      ),
                    ),
                    SizedBox(height: 20),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: AuthTextField(
                        controller: emailController,
                        hintText: "Email",
                        obscureText: false,
                      ),
                    ),
                    SizedBox(height: 20),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: AuthTextField(
                        obscureText: isPasswordVisible,
                        controller: passwordController,
                        hintText: "Password",
                        icon: IconButton(
                          onPressed: () {
                            setState(() {
                              isPasswordVisible = !isPasswordVisible;
                            });
                          },
                          icon: Icon(
                            isPasswordVisible
                                ? Icons.visibility_off_outlined
                                : Icons.visibility_outlined,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 20),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: DropdownButtonFormField(
                        decoration: InputDecoration(
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: AppColors.grey2),
                          ),
                          hintText: "Role",
                          hintStyle: AppTextStyles.hintText,

                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: Role.designer,
                            child: Text(Role.designer),
                          ),
                          DropdownMenuItem(
                            value: Role.supervisor,
                            child: Text(Role.supervisor),
                          ),
                        ],
                        onChanged: (value) {
                          selectedRole = value;
                        },
                      ),
                    ),
                    SizedBox(height: 30),
                    // Spacer(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: PrimaryButton(
                        text: "REGISTER",
                        onPressed: () {
                          context.read<AuthCubit>().register(
                            emailController.text,
                            passwordController.text,
                            nameController.text,
                            selectedRole ?? "",
                          );
                        },
                        width: double.maxFinite,
                        isLoading: state.loading,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // floatingActionButton: Padding(
            //   padding: const EdgeInsets.symmetric(horizontal: 16),
            //   child: PrimaryButton(
            //     text: "REGISTER",
            //     onPressed: () {
            //       context.read<AuthCubit>().register(
            //         emailController.text,
            //         passwordController.text,
            //         nameController.text,
            //         selectedRole ?? "",
            //       );
            //     },
            //     width: double.maxFinite,
            //     isLoading: state.loading,
            //   ),
            // ),
            // floatingActionButtonLocation:
            //     FloatingActionButtonLocation.centerFloat,
          ),
        );
      },
    );
  }
}
