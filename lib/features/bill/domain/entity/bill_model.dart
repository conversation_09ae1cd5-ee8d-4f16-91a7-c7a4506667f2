import 'package:cloud_firestore/cloud_firestore.dart';

class BillModel {
  final String docId;
  final String projectId;
  final String billName;
  final String billDesc;
  final num billAmount;
  final String billUrl;
  final String billExtenstion;
  final String billFileName;
  final DateTime uploadAt;
  final String uploadBy;

  BillModel({
    required this.docId,
    required this.projectId,
    required this.billName,
    required this.billDesc,
    required this.billAmount,
    required this.billUrl,
    required this.billExtenstion,
    required this.billFileName,
    required this.uploadAt,
    required this.uploadBy,
  });

  /// CopyWith
  BillModel copyWith({
    String? docId,
    String? projectId,
    String? billName,
    String? billDesc,
    num? billAmount,
    String? billUrl,
    String? billExtenstion,
    String? billFileName,
    DateTime? uploadAt,
    String? uploadBy,
  }) {
    return BillModel(
      docId: docId ?? this.docId,
      projectId: projectId ?? this.projectId,
      billName: billName ?? this.billName,
      billDesc: billDesc ?? this.billDesc,
      billAmount: billAmount ?? this.billAmount,
      billUrl: billUrl ?? this.billUrl,
      billExtenstion: billExtenstion ?? this.billExtenstion,
      billFileName: billFileName ?? this.billFileName,
      uploadAt: uploadAt ?? this.uploadAt,
      uploadBy: uploadBy ?? this.uploadBy,
    );
  }

  /// From JSON
  factory BillModel.fromJson(Map<String, dynamic> json) {
    return BillModel(
      docId: json['docId'] ?? '',
      projectId: json['projectId'] ?? '',
      billName: json['billName'] ?? '',
      billDesc: json['billDesc'] ?? '',
      billAmount: json['billAmount'] ?? 0,
      billUrl: json['billUrl'] ?? '',
      billExtenstion: json['billExtenstion'] ?? '',
      billFileName: json['billFileName'] ?? '',
      uploadAt: (json['uploadAt'] as Timestamp).toDate(),
      uploadBy: json['uploadBy'] ?? '',
    );
  }

  /// To JSON
  Map<String, dynamic> toJson() {
    return {
      'projectId': projectId,
      'billName': billName,
      'billDesc': billDesc,
      'billAmount': billAmount,
      'billUrl': billUrl,
      'billExtenstion': billExtenstion,
      'uploadAt': Timestamp.fromDate(uploadAt),
      'billFileName': billFileName,
      'uploadBy': uploadBy,
    };
  }

  /// To Map (alias of toJson)
  Map<String, dynamic> toMap() => toJson();

  /// From Map (alias of fromJson)
  factory BillModel.fromMap(Map<String, dynamic> map) =>
      BillModel.fromJson(map);

  /// From Firestore DocumentSnapshot
  factory BillModel.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return BillModel.fromJson({
      ...data,
      'docId': snapshot.id, // override docId from document ID
    });
  }
}
