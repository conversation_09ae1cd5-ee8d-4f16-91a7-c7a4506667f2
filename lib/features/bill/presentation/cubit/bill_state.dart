part of 'bill_cubit.dart';

class BillState {
  final List<BillModel> bills;
  final String message;
  final bool isLoading;
  final String selectedType;

  BillState({
    required this.bills,
    required this.message,
    required this.isLoading,
    required this.selectedType,
  });

  factory BillState.initial() {
    return BillState(
      bills: [],
      message: '',
      isLoading: false,
      selectedType: 'All',
    );
  }

  BillState copyWith({
    List<BillModel>? bills,
    String? message,
    bool? isLoading,
    dynamic selectedFile,
    String? selectedType,
  }) {
    return BillState(
      bills: bills ?? this.bills,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      selectedType: selectedType ?? this.selectedType,
    );
  }
}
