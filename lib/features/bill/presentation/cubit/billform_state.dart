part of 'billform_cubit.dart';

class BillFormState {
  final String? dbFile;
  final SelectedImage? selectedFile;
  final bool isLoading;
  final String message;

  BillFormState({
    this.dbFile,
    this.selectedFile,
    required this.isLoading,
    required this.message,
  });

  factory BillFormState.initial() {
    return BillFormState(
      dbFile: null,
      selectedFile: null,
      isLoading: false,
      message: '',
    );
  }

  BillFormState copyWith({
    dynamic dbFile,
    dynamic selectedFile,
    bool? isLoading,
    String? message,
  }) {
    return BillFormState(
      dbFile: dbFile is bool ? null : (dbFile ?? this.dbFile),
      selectedFile:
          selectedFile is bool ? null : (selectedFile ?? this.selectedFile),

      isLoading: isLoading ?? this.isLoading,
      message: message ?? this.message,
    );
  }
}
