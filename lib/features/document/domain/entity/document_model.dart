import 'package:cloud_firestore/cloud_firestore.dart';

class DocumentModel {
  final String docId;
  final String projectId;
  final String documentName;
  final String documentUrl;
  final String docExtenstion;
  final String docFileName;
  final DateTime uploadAt;
  final String uploadBy;

  DocumentModel({
    required this.docId,
    required this.projectId,
    required this.documentName,
    required this.documentUrl,
    required this.docExtenstion,
    required this.docFileName,
    required this.uploadAt,
    required this.uploadBy,
  });

  DocumentModel copyWith({
    String? docId,
    String? projectId,
    String? documentName,
    String? documentUrl,
    String? docExtenstion,
    String? docFileName,
    DateTime? uploadAt,
    String? uploadBy,
  }) {
    return DocumentModel(
      docId: docId ?? this.docId,
      projectId: projectId ?? this.projectId,
      documentName: documentName ?? this.documentName,
      documentUrl: documentUrl ?? this.documentUrl,
      docExtenstion: docExtenstion ?? this.docExtenstion,
      docFileName: docFileName ?? this.docFileName,
      uploadAt: uploadAt ?? this.uploadAt,
      uploadBy: uploadBy ?? this.uploadBy,
    );
  }

  /// From JSON
  factory DocumentModel.fromJson(Map<String, dynamic> json) {
    return DocumentModel(
      docId: json['docId'] ?? '',
      projectId: json['projectId'] ?? '',
      documentName: json['documentName'] ?? '',
      documentUrl: json['documentUrl'] ?? '',
      docExtenstion: json['docExtenstion'] ?? '',
      docFileName: json['docFileName'] ?? '',
      uploadAt:
          (json['uploadAt'] is Timestamp)
              ? (json['uploadAt'] as Timestamp).toDate()
              : DateTime.tryParse(json['uploadAt'] ?? '') ?? DateTime.now(),
      uploadBy: json['uploadBy'] ?? '',
    );
  }

  /// To JSON
  Map<String, dynamic> toJson() {
    return {
      'projectId': projectId,
      'documentName': documentName,
      'documentUrl': documentUrl,
      'docExtenstion': docExtenstion,
      'docFileName': docFileName,
      'uploadAt': Timestamp.fromDate(uploadAt),
      'uploadBy': uploadBy,
    };
  }

  /// From Firestore DocumentSnapshot
  factory DocumentModel.fromSnapshot(DocumentSnapshot doc) {
    final json = doc.data() as Map<String, dynamic>;
    return DocumentModel.fromJson({...json, 'docId': doc.id});
  }

  /// Optional: toMap is just alias of toJson
  Map<String, dynamic> toMap() => toJson();
}
