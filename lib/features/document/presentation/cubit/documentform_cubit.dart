import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/document/domain/entity/document_model.dart';
import 'package:cp_associates/features/document/domain/repo/document_repo.dart';
import 'package:cp_associates/features/stroage/data/firebase_storage_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
part 'documentform_state.dart';

class DocumentFormCubit extends Cubit<DocumentFormState> {
  final DocumentRepo documentRepo;

  DocumentFormCubit(this.documentRepo) : super(DocumentFormState.initial());

  final nameController = TextEditingController();

  final formKey = GlobalKey<FormState>();

  void initializeForm(DocumentModel? editDocument) {
    emit(state.copyWith(message: "", isLoading: true));
    if (editDocument != null) {
      nameController.text = editDocument.documentName;

      emit(
        state.copyWith(
          dbFile: editDocument.documentUrl,
          isLoading: false,
          selectedFile: null,
        ),
      );
    } else {
      emit(DocumentFormState.initial());
    }
  }

  Future<void> pickFile(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickFile(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  Future<void> pickFileFromCamera(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickImageNewCamera(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  Future<void> viewPickFile(
    String? dbImg,
    BuildContext context,
    String? dbImgExt,
  ) async {
    await viewFile(
      context: context,
      selectedFile: state.selectedFile,
      dbImg: dbImg,
      dbImgExt: dbImgExt,
    );
  }

  void deletPickFile(bool dbImage) {
    if (dbImage) {
      emit(state.copyWith(dbFile: false));
    } else {
      emit(state.copyWith(selectedFile: false));
    }
  }

  Future<void> submit({
    required BuildContext context,
    required String projectId,
    DocumentModel? editDocument,
  }) async {
    emit(state.copyWith(isLoading: true, message: ""));

    final name = nameController.text.trim();
    if (name.isEmpty ||
        (state.selectedFile == null && editDocument?.documentUrl == null)) {
      Navigator.of(context).pop();
      emit(
        state.copyWith(
          isLoading: false,
          message: "Please fill all fields and upload a document",
        ),
      );
      return;
    }

    try {
      final fileUrl =
          state.selectedFile != null
              ? await FirebaseStorageRepo().uploadDocumentsFile(
                state.selectedFile!,
              )
              : editDocument?.documentUrl;

      final doc = DocumentModel(
        docId: editDocument?.docId ?? "",
        documentName: name,
        documentUrl: fileUrl ?? "",
        docExtenstion:
            state.selectedFile?.extension ?? editDocument?.docExtenstion ?? "",
        docFileName:
            state.selectedFile?.name ?? editDocument?.docFileName ?? "",
        projectId: projectId,
        uploadAt: DateTime.now(),
        uploadBy: FBAuth.auth.currentUser?.uid ?? "",
      );

      if (editDocument != null) {
        await documentRepo.updateDocument(doc);
        emit(
          state.copyWith(
            isLoading: false,
            message: "Document updated successfully",
          ),
        );
        Navigator.pop(context);
        return;
      } else {
        await documentRepo.uploadDocument(doc);
        emit(
          state.copyWith(
            isLoading: false,
            message: "Document uploaded successfully",
          ),
        );
        Navigator.pop(context);
        return;
      }

      // emit(DocumentFormState.initial());
      // Navigator.pop(context); // close the form
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to upload document: ${e.toString()}",
        ),
      );
    }
  }
}
