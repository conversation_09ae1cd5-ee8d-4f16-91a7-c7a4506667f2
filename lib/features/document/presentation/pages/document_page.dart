import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/filter_container.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/document/presentation/cubit/document_cubit.dart';
import 'package:cp_associates/features/document/presentation/cubit/document_state.dart';
import 'package:cp_associates/features/document/presentation/cubit/documentform_cubit.dart';
import 'package:cp_associates/features/document/presentation/widgets/document_detail_tile.dart';
import 'package:cp_associates/features/document/presentation/widgets/document_form.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DocumentPage extends StatefulWidget {
  final String projectId;
  DocumentPage({super.key, required this.projectId});

  @override
  State<DocumentPage> createState() => _DocumentPageState();
}

class _DocumentPageState extends State<DocumentPage> {
  @override
  Widget build(BuildContext context) {
    final documentCubit = context.read<DocumentCubit>();
    return BlocConsumer<DocumentCubit, DocumentState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        print("Document Cubit build");
        return ResponsiveCustomBuilder(
          mobileBuilder: (width) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,

              children: [
                filterTabs(context, state),
                SizedBox(height: 20),
                Expanded(
                  child: Stack(
                    children: [
                      DocumentDetailTile(projectId: widget.projectId),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 20),
                        child: Container(
                          alignment: Alignment.bottomRight,
                          child: AddBtn(
                            text: "upload",
                            onPressed: () {
                              if (!context
                                  .read<ProjectCubit>()
                                  .state
                                  .haveAccess) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      "You are not authorized to perform this action.",
                                    ),
                                  ),
                                );
                                return;
                              }
                              showModalBottomSheet(
                                useSafeArea: true,
                                isScrollControlled: true,
                                context: context,
                                builder: (context) {
                                  return Padding(
                                    padding: MediaQuery.of(context).viewInsets,
                                    child: BlocProvider(
                                      create:
                                          (context) => DocumentFormCubit(
                                            documentCubit.documentRepo,
                                          ),
                                      child: DocumentForm(
                                        projectId: widget.projectId,
                                        editDocument: null,
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                            color: Color(0xffD57A49),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 70),
              ],
            );
          },
          desktopBuilder: (width) {
            return Column(
              children: [
                filterTabs(context, state),
                SizedBox(height: 20),
                Expanded(
                  child: Stack(
                    children: [
                      DocumentDetailTile(projectId: widget.projectId),
                      Container(
                        alignment: Alignment.bottomRight,
                        child: AddBtn(
                          text: "upload",
                          onPressed: () {
                            if (!context
                                .read<ProjectCubit>()
                                .state
                                .haveAccess) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    "You are not authorized to perform this action.",
                                  ),
                                ),
                              );
                              return;
                            }
                            showDialog(
                              context: context,
                              builder: (context) {
                                return Dialog(
                                  child: Container(
                                    width: 500,
                                    child: BlocProvider(
                                      create:
                                          (context) => DocumentFormCubit(
                                            documentCubit.documentRepo,
                                          ),
                                      child: DocumentForm(
                                        projectId: widget.projectId,
                                        editDocument: null,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                          color: Color(0xffD57A49),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Row filterTabs(BuildContext context, DocumentState state) {
    return Row(
      children: [
        FilterContainer(
          title: "All",
          onFilterTap: () {
            context.read<DocumentCubit>().filterDocumentsByType(docTypes.All);
          },
          isSelected: state.selectedType == docTypes.All,
        ),
        SizedBox(width: 10),
        FilterContainer(
          title: "PDF",
          onFilterTap: () {
            context.read<DocumentCubit>().filterDocumentsByType(docTypes.PDF);
          },
          isSelected: state.selectedType == docTypes.PDF,
        ),
        SizedBox(width: 10),
        FilterContainer(
          title: "Images",
          onFilterTap: () {
            context.read<DocumentCubit>().filterDocumentsByType(
              docTypes.Images,
            );
          },
          isSelected: state.selectedType == docTypes.Images,
        ),
        SizedBox(width: 10),
        FilterContainer(
          title: "Others",
          onFilterTap: () {
            context.read<DocumentCubit>().filterDocumentsByType(
              docTypes.Others,
            );
          },
          isSelected: state.selectedType == docTypes.Others,
        ),
      ],
    );
  }
}
