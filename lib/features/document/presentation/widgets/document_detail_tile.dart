import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/document/domain/entity/document_model.dart';
import 'package:cp_associates/features/document/presentation/cubit/document_cubit.dart';
import 'package:cp_associates/features/document/presentation/cubit/document_state.dart';
import 'package:cp_associates/features/document/presentation/cubit/documentform_cubit.dart';
import 'package:cp_associates/features/document/presentation/widgets/document_form.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DocumentDetailTile extends StatefulWidget {
  String projectId;
  DocumentDetailTile({super.key, required this.projectId});

  @override
  State<DocumentDetailTile> createState() => _DocumentDetailTileState();
}

class _DocumentDetailTileState extends State<DocumentDetailTile> {
  void initState() {
    context.read<DocumentCubit>().fetchDocuments(widget.projectId);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final documentCubit = context.read<DocumentCubit>();
    return BlocBuilder<DocumentCubit, DocumentState>(
      builder: (context, state) {
        if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        } else if (state.documents.isNotEmpty) {
          List<DocumentModel> filteredDocuments = [];
          if (state.selectedType == docTypes.All) {
            filteredDocuments = state.documents;
          } else if (state.selectedType == docTypes.PDF) {
            filteredDocuments =
                state.documents
                    .where(
                      (document) =>
                          document.docExtenstion.toLowerCase() ==
                          docTypes.PDF.toLowerCase(),
                    )
                    .toList();
          } else if (state.selectedType == docTypes.Images) {
            filteredDocuments =
                state.documents
                    .where(
                      (document) => imageExtensions.contains(
                        document.docExtenstion.toLowerCase(),
                      ),
                    )
                    .toList();
          } else if (state.selectedType == docTypes.Others) {
            filteredDocuments =
                state.documents
                    .where(
                      (document) =>
                          !imageExtensions.contains(
                            document.docExtenstion.toLowerCase(),
                          ) &&
                          document.docExtenstion.toLowerCase() !=
                              docTypes.PDF.toLowerCase(),
                    )
                    .toList();
          }
          return filteredDocuments.isEmpty
              ? Center(child: Text("No Document found"))
              : SingleChildScrollView(
                child: Column(
                  spacing: 15,
                  children: [
                    ...List.generate(filteredDocuments.length, (index) {
                      final DocumentModel document = filteredDocuments[index];
                      return InkWell(
                        onTap: () {
                          documentCubit.viewDocument(document, context);
                        },
                        onDoubleTap: () {
                          kIsWeb
                              ? showDialog(
                                context: context,
                                builder: (context) {
                                  return Dialog(
                                    child: Container(
                                      width: 500,
                                      child: BlocProvider(
                                        create:
                                            (context) => DocumentFormCubit(
                                              documentCubit.documentRepo,
                                            ),
                                        child: DocumentForm(
                                          projectId: widget.projectId,
                                          editDocument: document,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              )
                              : showModalBottomSheet(
                                useSafeArea: true,
                                isScrollControlled: true,
                                context: context,
                                builder: (context) {
                                  return Padding(
                                    padding: MediaQuery.of(context).viewInsets,
                                    child: BlocProvider(
                                      create:
                                          (context) => DocumentFormCubit(
                                            documentCubit.documentRepo,
                                          ),
                                      child: DocumentForm(
                                        projectId: widget.projectId,
                                        editDocument: document,
                                      ),
                                    ),
                                  );
                                },
                              );
                        },
                        onLongPress: () {
                          showConfirmDeletDialog(context, () {
                            documentCubit.deleteDocument(document.docId);
                          });
                        },
                        child: Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: AppColors.chipGreyColor,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(CupertinoIcons.doc, size: 24),
                            ),
                            SizedBox(width: 15),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  document.documentName,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                SizedBox(height: 5),
                                Text(
                                  document.uploadAt.goodDayDate(),
                                  style: TextStyle(color: AppColors.grey2),
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    }),
                  ],
                ),
              );
        } else if (state.documents.isEmpty) {
          return Center(child: Text("No Document found"));
        } else if (state.message.isNotEmpty) {
          return Center(child: Text(state.message.toString()));
        } else {
          return Center();
        }
      },
    );
  }
}
