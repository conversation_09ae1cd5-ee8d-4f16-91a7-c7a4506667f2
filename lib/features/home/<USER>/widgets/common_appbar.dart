import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/attendances/presentation/widget/request_dialog.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:cp_associates/features/mastertask/presentation/widget/mastertask_dialog.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_form_cubit.dart';
import 'package:cp_associates/features/users/presentation/pages/userprofile_page.dart';
import 'package:cp_associates/features/users/presentation/widgets/changepassword_form.dart';
import 'package:cp_associates/features/users/presentation/widgets/editprofile_form.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class CommonAppBar extends StatefulWidget {
  CommonAppBar({super.key});

  @override
  State<CommonAppBar> createState() => _CommonAppBarState();
}

class _CommonAppBarState extends State<CommonAppBar> {
  final GlobalKey _avatarKey = GlobalKey();
  final OverlayPortalController _controller = OverlayPortalController();

  @override
  Widget build(BuildContext context) {
    final authCubit = context.read<AuthCubit>();
    final mediaQuerySize = MediaQuery.sizeOf(context).width;
    return AppBar(
      title: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          String userEmailInitial = "U"; // default placeholder

          if (state.isAuthenticated) {
            userEmailInitial = state.currentUser?.email[0].toUpperCase() ?? "";
          }

          return Padding(
            padding: EdgeInsets.symmetric(
              horizontal: mediaQuerySize > 768 ? 100 : 0,
            ),
            child: Row(
              children: [
                // Logo
                InkWell(
                  onTap: () {
                    context.go(Routes.home);
                  },
                  child: Image.asset(
                    "assets/images/logo_light.png",
                    color: Colors.black,
                    width: 66,
                    height: 33,
                  ),
                ),
                Spacer(),
                // Master Task
                InkWell(
                  onTap: () {
                    kIsWeb
                        ? showDialog(
                          context: context,
                          builder: (context) {
                            return MasterTaskDialog();
                          },
                        )
                        : context.push(Routes.masterTask);
                  },
                  child: Container(
                    padding: EdgeInsets.all(5),
                    decoration: BoxDecoration(
                      color: AppColors.containerGreyColor,
                      border: Border.all(color: AppColors.borderGrey),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(Icons.checklist, size: 20),
                  ),
                ),
                SizedBox(width: 10),

                // Notification only for admin
                authCubit.state.currentUser?.role == "admin"
                    ? BlocBuilder<AttendanceAdminCubit, AttendanceAdminState>(
                      builder: (context, state) {
                        final attendanceAdminCubit =
                            context.read<AttendanceAdminCubit>();
                        return InkWell(
                          onTap: () {
                            kIsWeb
                                ? showDialog(
                                  context: context,
                                  builder: (context) {
                                    return RequestDialog();
                                  },
                                )
                                : context.push(Routes.request);
                          },

                          child:
                              attendanceAdminCubit.state.requests.isNotEmpty
                                  ? Badge(
                                    label: Text(
                                      "${attendanceAdminCubit.state.requests.length}",
                                      style: TextStyle(color: Colors.white),
                                    ),

                                    child: Icon(CupertinoIcons.bell),
                                  )
                                  : Icon(CupertinoIcons.bell),
                        );
                      },
                    )
                    : SizedBox(),

                authCubit.state.currentUser?.role == "admin"
                    ? SizedBox(width: 10)
                    : SizedBox(),

                // Users only for admin
                authCubit.state.currentUser?.role == "admin"
                    ? InkWell(
                      onTap: () {
                        kIsWeb
                            ? context.go(Routes.users)
                            : context.push(Routes.users);
                      },
                      child: Container(
                        padding: EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          color: AppColors.containerGreyColor,
                          border: Border.all(color: AppColors.borderGrey),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(CupertinoIcons.person_2, size: 20),
                      ),
                    )
                    : SizedBox(),
                authCubit.state.currentUser?.role == "admin"
                    ? SizedBox(width: 10)
                    : SizedBox(),
                kIsWeb
                    ? InkWell(
                      key: _avatarKey,
                      onTap: () {
                        setState(() {
                          _controller.toggle(); // Toggle overlay
                        });
                      },
                      child: OverlayPortal(
                        controller: _controller,
                        overlayChildBuilder: (context) {
                          final RenderBox renderBox =
                              _avatarKey.currentContext?.findRenderObject()
                                  as RenderBox;
                          final Offset position = renderBox.localToGlobal(
                            Offset.zero,
                          );
                          return Positioned(
                            top: position.dy + 50,
                            left: position.dx - 300,
                            width: 400,
                            child: Material(
                              elevation: 4,
                              borderRadius: BorderRadius.circular(8),
                              child: Container(
                                padding: EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: AppColors.containerGreyColor,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: AppColors.borderGrey,
                                  ),
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    ProfileTile(
                                      isMobile: false,
                                      icon: Icon(CupertinoIcons.person),
                                      title: "Edit Profile",
                                      onTap: () {
                                        showDialog(
                                          useSafeArea: true,
                                          context: context,
                                          builder: (context) {
                                            return BlocProvider(
                                              create:
                                                  (context) => UserFormCubit(
                                                    context
                                                        .read<AuthCubit>()
                                                        .authRepo,
                                                  ),
                                              child: Dialog(
                                                child: Padding(
                                                  padding:
                                                      MediaQuery.of(
                                                        context,
                                                      ).viewInsets,
                                                  child: Container(
                                                    constraints: BoxConstraints(
                                                      maxWidth: 400,
                                                    ),
                                                    child: EditProfileForm(
                                                      editUser: null,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        );
                                      },
                                    ),
                                    SizedBox(height: 10),
                                    ProfileTile(
                                      isMobile: false,
                                      icon: Icon(CupertinoIcons.lock),
                                      title: "Change Password",
                                      onTap: () {
                                        showDialog(
                                          useSafeArea: true,
                                          context: context,
                                          builder: (context) {
                                            return BlocProvider(
                                              create:
                                                  (context) => UserFormCubit(
                                                    context
                                                        .read<AuthCubit>()
                                                        .authRepo,
                                                  ),
                                              child: Dialog(
                                                child: Padding(
                                                  padding:
                                                      MediaQuery.of(
                                                        context,
                                                      ).viewInsets,
                                                  child: Container(
                                                    constraints: BoxConstraints(
                                                      maxWidth: 400,
                                                    ),
                                                    child: ChangePasswordForm(),
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        );
                                      },
                                    ),
                                    SizedBox(height: 10),
                                    ProfileTile(
                                      isMobile: false,
                                      icon: Icon(CupertinoIcons.trash),
                                      title: "Delete Account",
                                      onTap: () {},
                                    ),
                                    SizedBox(height: 10),
                                    ProfileTile(
                                      isMobile: false,
                                      icon: Icon(
                                        CupertinoIcons.square_arrow_right,
                                      ),
                                      title: "Logout",
                                      onTap: () {
                                        showLogoutDialog(context, () {
                                          context.read<AuthCubit>().logOut(
                                            context,
                                          );
                                        });
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: 15,
                              child: Text(userEmailInitial),
                            ),
                            SizedBox(width: 10),
                            Text(
                              state.currentUser?.name ?? "",
                              style: TextStyle(fontSize: 20),
                            ),
                          ],
                        ),
                      ),
                    )
                    : InkWell(
                      onTap: () {
                        context.push("${Routes.usersDetails}");
                      },
                      child: CircleAvatar(
                        radius: 15,
                        child: Text(userEmailInitial),
                      ),
                    ),
              ],
            ),
          );
        },
      ),
      bottom: PreferredSize(
        child:
            mediaQuerySize > 768
                ? Container(color: AppColors.borderGrey, height: 2)
                : SizedBox(),
        preferredSize: Size.fromHeight(4.0),
      ),
    );
  }
}
