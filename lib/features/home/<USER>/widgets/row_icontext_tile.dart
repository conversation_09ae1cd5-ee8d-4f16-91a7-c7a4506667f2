import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class RowIconTextTile extends StatelessWidget {
  RowIconTextTile({
    super.key,
    required this.title,
    this.count,
    required this.SuffixWidget,
    required this.onTap,
    required this.isMobile,
    required this.isLoading,
  });

  String title;
  String? count;
  Widget SuffixWidget;
  Function() onTap;
  bool isMobile;
  bool isLoading;

  @override
  Widget build(BuildContext context) {
    return TransparentInkWell(
      onTap: () => onTap(),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.borderGrey),
          color: AppColors.containerGreyColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child:
            isLoading
                ? Center(child: CircularProgressIndicator())
                : Row(
                  children: [
                    Container(
                      height: isMobile ? 55 : 50,
                      width: isMobile ? 55 : 50,
                      padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color:
                            isMobile
                                ? AppColors.secondary
                                : AppColors.containerGreyColor,
                        border: Border.all(
                          color:
                              isMobile
                                  ? AppColors.borderGrey
                                  : Colors.transparent,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(child: SuffixWidget),
                    ),
                    SizedBox(width: isMobile ? 10 : 20),
                    isMobile
                        ? Text(
                          title,
                          style: TextStyle(
                            fontSize: isMobile ? 16 : 20,
                            fontWeight: FontWeight.w900,
                          ),
                        )
                        : Text(
                          "${title}  ${count != null ? "(${count})" : ""}",
                          style: TextStyle(
                            fontSize: isMobile ? 16 : 20,
                            fontWeight: FontWeight.w900,
                          ),
                        ),
                    Spacer(),
                    IconButton(
                      onPressed: () {
                        onTap();
                      },
                      icon: Icon(
                        CupertinoIcons.chevron_right,
                        size: isMobile ? 20 : 25,
                      ),
                    ),
                  ],

                  // children: [Text("Add Task")],
                ),
      ),
    );
  }
}
