part of 'master_task_cubit.dart';

@immutable
class MasterTaskState {
  final List<MasterTaskModel> masterTask;
  final String message;
  final bool isLoading;

  MasterTaskState({
    required this.masterTask,
    required this.message,
    required this.isLoading,
  });

  factory MasterTaskState.initial() {
    return MasterTaskState(masterTask: [], message: '', isLoading: false);
  }

  MasterTaskState copyWith({
    List<MasterTaskModel>? masterTask,
    String? message,
    bool? isLoading,
  }) {
    return MasterTaskState(
      masterTask: masterTask ?? this.masterTask,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
