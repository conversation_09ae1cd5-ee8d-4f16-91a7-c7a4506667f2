import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/mastertask/domain/entity/master_task_model.dart';
import 'package:cp_associates/features/mastertask/domain/repo/mastertask_repo.dart';

class FirebaseMasterTaskRepo implements MasterTaskRepo {
  final masterTaskRef = FBFireStore.masterTasks;

  @override
  Future<void> createMasterTask(MasterTaskModel masterTask) async {
    final docRef = masterTaskRef.doc();
    final newMasterTask = masterTask.copyWith(docId: docRef.id);
    await docRef.set(newMasterTask.toJson());
  }

  @override
  Future<void> updateMasterTask(MasterTaskModel masterTask) async {
    await masterTaskRef.doc(masterTask.docId).update(masterTask.toJson());
  }

  @override
  Future<void> deleteMasterTask(String masterTaskId) async {
    await masterTaskRef.doc(masterTaskId).delete();
  }

  @override
  Stream<List<MasterTaskModel>> getAllMasterTask() {
    return masterTaskRef.snapshots().map((snapshot) {
      return snapshot.docs
          .map((doc) => MasterTaskModel.fromSnapshot(doc))
          .toList();
    });
  }
}
