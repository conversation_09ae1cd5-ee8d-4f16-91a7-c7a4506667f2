import 'package:cloud_firestore/cloud_firestore.dart';

class MasterTaskModel {
  final String docId;
  final DateTime createdAt;
  final String createdBy;
  final String title;
  final String desc;
  final String? assignTo;
  final int duration;
  // final String priority;
  // final String status;
  // final String projectId;
  // final DateTime? startDate;
  // final DateTime endDate;
  // final String? attachments;
  // final String? attachmentType;
  // final String? attachmentName;

  MasterTaskModel({
    required this.docId,
    required this.createdAt,
    required this.createdBy,
    required this.title,
    required this.desc,
    required this.duration,
    required this.assignTo,
    // required this.priority,
    // required this.status,
    // required this.projectId,
    // required this.startDate,
    // required this.endDate,
    // required this.attachments,
    // required this.attachmentType,
    // required this.attachmentName,
  });

  // copyWith method
  MasterTaskModel copyWith({
    String? docId,
    DateTime? createdAt,
    String? createdBy,
    String? title,
    String? desc,
    String? priority,
    String? status,
    String? assignTo,
    int? duration,
    // String? projectId,
    // DateTime? startDate,
    // DateTime? endDate,
    // List<String>? assignTo,
    // String? attachments,
    // String? attachmentType,
    // String? attachmentName,
  }) {
    return MasterTaskModel(
      docId: docId ?? this.docId,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      title: title ?? this.title,
      desc: desc ?? this.desc,
      assignTo: assignTo ?? this.assignTo,
      duration: duration ?? this.duration,
      // priority: priority ?? this.priority,
      // status: status ?? this.status,
      // projectId: projectId ?? this.projectId,
      // startDate: startDate ?? this.startDate,
      // endDate: endDate ?? this.endDate,
      // assignTo: assignTo ?? this.assignTo,
      // attachments: attachments ?? this.attachments,
      // attachmentType: attachmentType ?? this.attachmentType,
      // attachmentName: attachmentName ?? this.attachmentName,
    );
  }

  /// Firestore snapshot to TaskModel
  factory MasterTaskModel.fromSnapshot(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return MasterTaskModel(
      docId: snap.id,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      desc: data['desc'] ?? '',
      assignTo: data['assignTo'] ?? '',
      title: data['title'] ?? '',
      duration: data['duration'] ?? 0,
      // priority: data['priority'] ?? '',
      // status: data['status'] ?? '',
      // projectId: data['projectId'] ?? '',
      // startDate:
      //     data['startDate'] != null
      //         ? (data['startDate'] as Timestamp).toDate()
      //         : null,
      // endDate: (data['endDate'] as Timestamp).toDate(),
      // attachments: data['attachments'],
      // attachmentType: data['attachmentType'],
      // attachmentName: data['attachmentName'],
    );
  }

  /// JSON to TaskModel
  factory MasterTaskModel.fromJson(Map<String, dynamic> json) {
    return MasterTaskModel(
      docId: json['docId'] ?? '',
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      createdBy: json['createdBy'] ?? '',
      title: json['title'] ?? '',
      desc: json['desc'] ?? '',
      assignTo: json['assignTo'] ?? '',
      duration: json['duration'] ?? 0,
      // priority: json['priority'] ?? '',
      // status: json['status'] ?? '',
      // projectId: json['projectId'] ?? '',
      // startDate:
      //     json['startDate'] != null
      //         ? (json['startDate'] as Timestamp).toDate()
      //         : null,
      // endDate: (json['endDate'] as Timestamp).toDate(),
      // attachments: json['attachments'] ?? null,
      // attachmentType: json['attachmentType'] ?? null,
      // attachmentName: json['attachmentName'] ?? null,
    );
  }

  /// Convert TaskModel to Firestore map (for upload/update)
  Map<String, dynamic> toMap() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'title': title,
      'desc': desc,
      'assignTo': assignTo,
      'duration': duration,
      // 'priority': priority,
      // 'status': status,
      // 'projectId': projectId,
      // 'startDate': startDate != null ? Timestamp.fromDate(startDate!) : null,
      // 'endDate': Timestamp.fromDate(endDate),
      // 'attachments': attachments,
      // 'attachmentType': attachmentType,
    };
  }

  /// Convert TaskModel to JSON (for local storage/API)
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'createdAt': createdAt,
      'createdBy': createdBy,
      'title': title,
      'desc': desc,
      'assignTo': assignTo,
      'duration': duration,

      // 'priority': priority,
      // 'status': status,
      // 'projectId': projectId,
      // 'startDate': startDate,
      // 'endDate': endDate,
      // 'assignTo': assignTo,
      // 'attachments': attachments,
      // 'attachmentType': attachmentType,
      // 'attachmentName': attachmentName,
    };
  }
}
