import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/features/mastertask/cubit/master_task_cubit.dart';
import 'package:cp_associates/features/mastertask/cubit/master_task_form_cubit.dart';
import 'package:cp_associates/features/mastertask/presentation/widget/mastertask_form.dart';
import 'package:cp_associates/features/mastertask/presentation/widget/mastertask_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/theme/app_text_styles.dart';

class MasterTaskPage extends StatefulWidget {
  const MasterTaskPage({super.key});

  @override
  State<MasterTaskPage> createState() => _MasterTaskPageState();
}

class _MasterTaskPageState extends State<MasterTaskPage> {
  @override
  void initState() {
    // context.read<MasterTaskCubit>().fetchAllMasterTask();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Master Task", style: AppTextStyles.appBarHeading),
      ),
      body: BlocConsumer<MasterTaskCubit, MasterTaskState>(
        listener: (context, state) {
          if (state.message.isNotEmpty) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.message)));
          }
        },
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            // padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      MasterTaskTile(),
                      Align(
                        alignment: Alignment.bottomRight,
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 20),
                          child: Container(
                            alignment: Alignment.bottomRight,
                            child: AddBtn(
                              text: "Add Task",

                              onPressed: () {
                                showModalBottomSheet(
                                  isScrollControlled: true,
                                  context: context,
                                  builder: (context) {
                                    return Padding(
                                      padding:
                                          MediaQuery.of(context).viewInsets,
                                      child: BlocProvider(
                                        create:
                                            (context) => MasterTaskFormCubit(
                                              context
                                                  .read<MasterTaskCubit>()
                                                  .masterTaskRepo,
                                            ),
                                        child: MasterTaskForm(
                                          editMasterTask: null,
                                        ),
                                      ),
                                    );
                                  },
                                );
                              },
                              color: AppColors.secondary,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 30),
              ],
            ),
          );
        },
      ),
    );
  }
}
