import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/transparent_inkwell.dart';
import 'package:cp_associates/features/mastertask/cubit/master_task_cubit.dart';
import 'package:cp_associates/features/mastertask/cubit/master_task_form_cubit.dart';
import 'package:cp_associates/features/mastertask/presentation/widget/mastertask_form.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MasterTaskTile extends StatefulWidget {
  const MasterTaskTile({super.key});

  @override
  State<MasterTaskTile> createState() => _MasterTaskTileState();
}

class _MasterTaskTileState extends State<MasterTaskTile> {
  void initState() {
    context.read<MasterTaskCubit>().fetchAllMasterTask();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    context.read<MasterTaskCubit>();
    return BlocConsumer<MasterTaskCubit, MasterTaskState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state.masterTask.isEmpty) {
          return const Center(child: Text("No  Task Avaliable"));
        } else {
          return SingleChildScrollView(
            child: Column(
              spacing: 15,
              mainAxisSize: MainAxisSize.min,
              children: [
                // filterTabs(context, state),
                ...List.generate(state.masterTask.length, (index) {
                  final masterTask = state.masterTask[index];

                  return TransparentInkWell(
                    onLongPress: () {
                      showConfirmDeletDialog(context, () {
                        context.read<MasterTaskCubit>().deleteMasterTask(
                          masterTask.docId,
                        );
                      });
                    },
                    child: Container(
                      // height: widget.isMobile ? null : 190,
                      padding: EdgeInsets.all(15),
                      decoration: BoxDecoration(
                        color: AppColors.containerGreyColor,
                        border: Border.all(color: AppColors.borderGrey),
                        borderRadius: BorderRadius.circular(12),
                      ),

                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            // mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: getColorFromInput(masterTask.title[0]),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                height: 45,
                                width: 45,
                                child: Center(
                                  child: Text(
                                    masterTask.title[0].toUpperCase(),
                                    // "test",
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 15,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 15),

                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    masterTask.title,
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),

                                  Text(
                                    masterTask.desc,
                                    style: TextStyle(fontSize: 14),
                                  ),
                                ],
                              ),
                              Spacer(),
                              InkWell(
                                onTap: () {
                                  kIsWeb
                                      ? showDialog(
                                        context: context,
                                        builder: (context) {
                                          return Dialog(
                                            child: Container(
                                              constraints: BoxConstraints(
                                                maxWidth: 400,
                                              ),
                                              padding: EdgeInsets.symmetric(
                                                vertical: 10,
                                                horizontal: 5,
                                              ),
                                              child: BlocProvider(
                                                create:
                                                    (context) =>
                                                        MasterTaskFormCubit(
                                                          context
                                                              .read<
                                                                MasterTaskCubit
                                                              >()
                                                              .masterTaskRepo,
                                                        ),
                                                child: MasterTaskForm(
                                                  editMasterTask: masterTask,
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      )
                                      : showModalBottomSheet(
                                        isScrollControlled: true,
                                        context: context,
                                        builder: (context) {
                                          return Padding(
                                            padding:
                                                MediaQuery.of(
                                                  context,
                                                ).viewInsets,
                                            child: BlocProvider(
                                              create:
                                                  (
                                                    context,
                                                  ) => MasterTaskFormCubit(
                                                    context
                                                        .read<MasterTaskCubit>()
                                                        .masterTaskRepo,
                                                  ),
                                              child: MasterTaskForm(
                                                editMasterTask: masterTask,
                                              ),
                                            ),
                                          );
                                        },
                                      );
                                },
                                child: Icon(CupertinoIcons.pen, size: 20),
                              ),
                              SizedBox(width: 15),
                              InkWell(
                                onTap: () {
                                  showConfirmDeletDialog(context, () {
                                    context
                                        .read<MasterTaskCubit>()
                                        .deleteMasterTask(masterTask.docId);
                                  });
                                },
                                child: Icon(CupertinoIcons.delete, size: 20),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                }),
                SizedBox(height: 50),
              ],
            ),
          );
        }
      },
    );
  }
}
