import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/domain/repo/project_repo.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class FirebaseProjectRepo implements ProjectRepo {
  final projectsRef = FBFireStore.projects;

  @override
  Future<void> createProject(ProjectModel project) async {
    final docRef = projectsRef.doc();
    final newProject = project.copyWith(docId: docRef.id);
    await docRef.set(newProject.toJson());
  }

  @override
  Future<void> updateProject(ProjectModel project) async {
    await projectsRef.doc(project.docId).update(project.toJson());
  }

  @override
  Future<void> deleteProject(String docId) async {
    await projectsRef.doc(docId).delete();
  }

  @override
  Stream<List<ProjectModel>> getAllProjects() {
    return projectsRef
        .where('projectStatus', isNotEqualTo: 'Finished')
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => ProjectModel.fromSnapshot(doc))
              .toList();
        });
  }

  Stream<List<ProjectModel>> getProjectsOfCurrentUser() {
    return projectsRef
        .where(
          Filter.or(
            Filter('userId', arrayContains: FBAuth.auth.currentUser?.uid ?? ''),
            Filter('createdBy', isEqualTo: FBAuth.auth.currentUser?.uid ?? ''),
          ),
        )
        .where('projectStatus', whereIn: ['Active', 'on-Hold'])
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => ProjectModel.fromSnapshot(doc))
              .toList();
        });
  }

  @override
  Future<List<ProjectModel>> getCompletedProjectsBetween(
    DateTime start,
    DateTime end,
  ) async {
    return projectsRef
        .where('projectStatus', isEqualTo: 'Finished')
        .where('projectStatusUpdateAt', isGreaterThanOrEqualTo: start)
        .where('projectStatusUpdateAt', isLessThan: end)
        .get()
        .then(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => ProjectModel.fromSnapshot(doc))
                  .toList(),
        );
  }

  @override
  Future<ProjectModel?> getProjectById(String id) async {
    try {
      print("FirebaseProjectRepo: Fetching project with ID: $id");
      final doc = await projectsRef.doc(id).get();

      if (!doc.exists) {
        print("FirebaseProjectRepo: Document does not exist for ID: $id");
        return null;
      }

      final data = doc.data();
      if (data == null) {
        print("FirebaseProjectRepo: Document data is null for ID: $id");
        return null;
      }

      print(
        "FirebaseProjectRepo: Successfully fetched project data for ID: $id",
      );
      return ProjectModel.fromJson(data);
    } catch (e) {
      print(
        "FirebaseProjectRepo: Error fetching project with ID $id: ${e.toString()}",
      );
      return null;
    }
  }

  Future<String> getTotalProjectCount() async {
    final countSnapshot = await projectsRef.count().get();
    return countSnapshot.count.toString();
  }
}
