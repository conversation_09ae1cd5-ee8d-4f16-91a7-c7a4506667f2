import 'package:cloud_firestore/cloud_firestore.dart';

class TranscationModel {
  DateTime createdAt;
  num amount;

  TranscationModel({required this.createdAt, required this.amount});

  // Convert TranscationModel instance to Map<String, dynamic> (JSON)
  Map<String, dynamic> toJson() {
    return {'createdAt': createdAt, 'amount': amount};
  }

  // Create a TranscationModel instance from Map<String, dynamic> (JSON)
  factory TranscationModel.fromJson(Map<String, dynamic> json) {
    return TranscationModel(
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      amount: json['amount'],
    );
  }
}
