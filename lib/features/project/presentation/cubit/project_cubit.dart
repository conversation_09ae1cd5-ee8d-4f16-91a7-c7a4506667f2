import 'dart:async';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/domain/repo/project_repo.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'project_state.dart';

class ProjectCubit extends Cubit<ProjectState> {
  final ProjectRepo repo;
  ProjectCubit(this.repo) : super(ProjectState.initial());
  StreamSubscription<List<ProjectModel>>? projectsStream;

  // fetch all projects for admin
  void fetchProjects() {
    emit(state.copyWith(isLoading: true, message: "", projects: []));

    projectsStream?.cancel();

    projectsStream = repo.getAllProjects().listen(
      (projects) {
        print("ProjectStreamAdmin------${projects.length}");
        emit(state.copyWith(projects: projects, isLoading: false, message: ""));
      },
      onError: (error) {
        print(error.toString());
        emit(state.copyWith(isLoading: false, message: error.toString()));
      },
    );
  }

  // fetch project detail
  void fetchProjectDetail(String id) async {
    print("fetchProjectDetail------");
    emit(state.copyWith(isLoading: true, message: ""));

    try {
      // First try to find project in current state
      final list = state.filteredProjects + state.projects;
      ProjectModel? project;

      if (list.isNotEmpty) {
        try {
          project = list.firstWhere((project) => project.docId == id);
        } catch (e) {
          project = null;
        }
      }

      // If project not found in state, fetch directly from repository
      if (project == null) {
        print("Project not found in state, fetching from repository...");
        project = await repo.getProjectById(id);
      }

      print("----From fetchProjectDetail------${project}");

      emit(
        state.copyWith(projectDetail: project, isLoading: false, message: ""),
      );
      print("----From fetchProjectDetail------${state.projectDetail}");
    } catch (e) {
      print("Error in fetchProjectDetail: ${e.toString()}");
      emit(
        state.copyWith(
          message: "Failed to load project: ${e.toString()}",
          isLoading: false,
        ),
      );
    }
  }

  // fetch project of current user and createdBy
  void fetchProjectsOfCurrentUser() {
    emit(state.copyWith(isLoading: true, message: "", projects: []));

    projectsStream?.cancel();

    projectsStream = repo.getProjectsOfCurrentUser().listen(
      (projects) {
        emit(state.copyWith(projects: projects, isLoading: false, message: ""));
        print("ProjectStreamUser------${projects.length}");
      },
      onError: (error) {
        print(error.toString());
        emit(state.copyWith(isLoading: false, message: error.toString()));
      },
    );
  }

  // fetch project by id (synchronous - only searches in current state)
  ProjectModel? fetchProjectById(String id) {
    try {
      final list = state.filteredProjects + state.projects;
      if (list.isNotEmpty) {
        try {
          return list.firstWhere((project) => project.docId == id);
        } catch (e) {
          return null;
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  deleteProject(String projectId) async {
    emit(state.copyWith(isLoading: true));

    await repo.deleteProject(projectId);
    emit(
      state.copyWith(isLoading: false, message: "Project deleted succesfully."),
    );
  }

  void checkUserAccess(
    ProjectModel? project,
    String userId,
    BuildContext context,
  ) {
    emit(
      state.copyWith(
        haveAccess:
            (project?.userId.contains(userId) ?? false) ||
            (context.read<AuthCubit>().state.currentUser?.role == "admin") ||
            (project?.projectStatus == ProjectStatus.finished),
      ),
    );
  }

  // ui related methods
  void totalProjectCount() async {
    await repo.getTotalProjectCount().then(
      (count) {
        emit(state.copyWith(totalProjectCount: count));
      },
      onError: (error) {
        emit(state.copyWith(message: error.toString()));
      },
    );
  }

  void selectedFilter(String filter) {
    emit(state.copyWith(selectedFilter: filter));
  }

  Future<void> filterCompletedProjects({
    required String filterType,
    DateTimeRange? customRange,
  }) async {
    emit(state.copyWith(isLoading: true, selectedFilter: filterType));

    DateTime now = DateTime.now();
    DateTime start;
    DateTime end;

    if (filterType == ProjectFilter.Monthly) {
      start = DateTime(now.year, now.month);
      end = DateTime(now.year, now.month + 1);
    } else if (filterType == ProjectFilter.Months6) {
      start = DateTime(now.year, now.month - 5);
      end = DateTime(now.year, now.month + 1);
    } else if (filterType == ProjectFilter.Custom && customRange != null) {
      start = customRange.start;
      end = customRange.end.add(const Duration(days: 1)); // inclusive
    } else {
      emit(state.copyWith(isLoading: false, message: 'Invalid filter'));
      return;
    }

    try {
      final projects = await repo.getCompletedProjectsBetween(start, end);
      emit(
        state.copyWith(
          filteredProjects: projects,
          isLoading: false,
          message: "",
        ),
      );
    } catch (e) {
      print(e.toString());
      emit(state.copyWith(isLoading: false, message: e.toString()));
    }
  }

  String getSupervisorName(List<String> userIds, BuildContext context) {
    final allUsers = context.read<UserCubit>().state.users;

    final supervisor = allUsers.firstWhere(
      (user) => userIds.contains(user.docId) && user.role == 'supervisor',
      orElse:
          () => UserModel(docId: "", name: "Not Assign", email: "", role: ""),
    );

    if (supervisor != null) {
      return supervisor.name;
    } else {
      return "";
    }
  }

  //unread notificationt functions

  Future<void> lastSeen(String projectId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      'last_seen_$projectId',
      DateTime.now().toIso8601String(),
    );
  }

  Future<bool> getLastSeen(ProjectModel project) async {
    // print("lastSeen: ${project.docId}--");
    final prefs = await SharedPreferences.getInstance();
    final seen = prefs.getString('last_seen_${project.docId}');

    final senTime = DateTime.tryParse(seen ?? '');
    print("lastSeen---: ${senTime}");
    print("updateLast---: ${project.lastUpdateAt}");
    print(senTime?.isBefore(project.lastUpdateAt));

    if (senTime == null) {
      return false;
    } else if (senTime.isBefore(project.lastUpdateAt)) {
      return true;
    } else {
      return false;
    }
  }

  @override
  Future<void> close() {
    projectsStream?.cancel();
    return super.close();
  }

  void clear() {
    emit(ProjectState.initial());
  }
}
