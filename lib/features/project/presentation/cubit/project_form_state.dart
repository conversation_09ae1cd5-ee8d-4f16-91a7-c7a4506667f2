part of 'project_form_cubit.dart';

@immutable
// class ProjectFormState {}
class ProjectFormState {
  final String message;
  final bool isLoading;
  SelectedImage? selectedFile;
  final String? dbFile;
  final List<String> selectedAssignUser;
  final List<TranscationModel> transcations;
  final String projectStatus;
  final DateTime? completedAt;
  // String selectedUser;
  // final String priorityStatus;
  // final DateTime? endDate;

  ProjectFormState({
    required this.message,
    required this.isLoading,
    required this.selectedFile,
    required this.dbFile,
    required this.selectedAssignUser,
    required this.transcations,
    // required this.selectedUser,
    required this.projectStatus,
    // required this.priorityStatus,
    required this.completedAt,
    // required this.endDate,
  });
  factory ProjectFormState.initial() {
    return ProjectFormState(
      message: '',
      isLoading: false,
      selectedFile: null,
      dbFile: null,
      selectedAssignUser: [],
      transcations: [],
      // selectedUser: '',
      projectStatus: ProjectStatus.active,
      // priorityStatus: '',
      completedAt: null,
      // endDate: null,
    );
  }

  ProjectFormState copyWith({
    String? message,
    bool? isLoading,
    dynamic selectedFile,
    dynamic dbFile,
    List<String>? selectedAssignUser,
    List<TranscationModel>? transcations,
    String? projectStatus,
    DateTime? completedAt,
    // String? selectedUser,
    // String? priorityStatus,
    // DateTime? endDate,
  }) {
    return ProjectFormState(
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      selectedFile:
          selectedFile is bool ? null : (selectedFile ?? this.selectedFile),
      dbFile: dbFile is bool ? null : (dbFile ?? this.dbFile),
      selectedAssignUser: selectedAssignUser ?? this.selectedAssignUser,
      transcations: transcations ?? this.transcations,
      // selectedUser: selectedUser ?? this.selectedUser,
      projectStatus: projectStatus ?? this.projectStatus,
      // priorityStatus: priorityStatus ?? this.priorityStatus,
      completedAt: completedAt ?? this.completedAt,
      // endDate: endDate ?? this.endDate,
    );
  }
}
