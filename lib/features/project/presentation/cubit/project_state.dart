part of 'project_cubit.dart';

@immutable
class ProjectState {
  final String totalProjectCount;
  final List<ProjectModel> projects;
  final List<ProjectModel> filteredProjects;
  final ProjectModel? projectDetail;
  final bool haveAccess;
  final String selectedFilter;
  final bool isLoading;
  final String message;

  ProjectState({
    required this.totalProjectCount,
    required this.projects,
    required this.filteredProjects,
    required this.projectDetail,
    required this.haveAccess,
    required this.selectedFilter,
    required this.isLoading,
    required this.message,
  });

  factory ProjectState.initial() {
    return ProjectState(
      totalProjectCount: "0",
      projects: [],
      filteredProjects: [],
      projectDetail: null,
      haveAccess: false,
      selectedFilter: ProjectFilter.Monthly,
      isLoading: false,
      message: "",
    );
  }

  ProjectState copyWith({
    final String? totalProjectCount,
    final List<ProjectModel>? projects,
    final List<ProjectModel>? filteredProjects,
    final ProjectModel? projectDetail,
    final bool? haveAccess,
    final String? selectedFilter,
    final bool? isLoading,
    final String? message,
  }) {
    return ProjectState(
      totalProjectCount: totalProjectCount ?? this.totalProjectCount,
      projects: projects ?? this.projects,
      filteredProjects: filteredProjects ?? this.filteredProjects,
      projectDetail: projectDetail ?? this.projectDetail,
      haveAccess: haveAccess ?? this.haveAccess,
      selectedFilter: selectedFilter ?? this.selectedFilter,
      isLoading: isLoading ?? this.isLoading,
      message: message ?? this.message,
    );
  }
}
