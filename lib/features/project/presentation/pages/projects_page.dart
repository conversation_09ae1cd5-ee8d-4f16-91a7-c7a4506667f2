import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/widgets/MonthYearDropdown.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/home/<USER>/widgets/common_appbar.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/project/presentation/widgets/projectdetail_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class ProjectsPage extends StatelessWidget {
  const ProjectsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProjectCubit, ProjectState>(
      builder: (context, state) {
        return ResponsiveWidCustom(
          mobile: BlocBuilder<ProjectCubit, ProjectState>(
            builder: (context, state) {
              return Scaffold(
                appBar: AppBar(
                  title: Text("Projects", style: AppTextStyles.appBarHeading),
                ),
                body: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  child: Column(
                    children: [
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            filterProjectWidget(
                              label: ProjectFilter.Monthly,
                              groupValue: state.selectedFilter,
                              context: context,
                              onTap: () {
                                context
                                    .read<ProjectCubit>()
                                    .filterCompletedProjects(
                                      filterType: ProjectFilter.Monthly,
                                    );
                              },
                            ),

                            const SizedBox(width: 10),
                            filterProjectWidget(
                              label: ProjectFilter.Months6,
                              groupValue: state.selectedFilter,
                              context: context,
                              onTap: () {
                                context
                                    .read<ProjectCubit>()
                                    .filterCompletedProjects(
                                      filterType: ProjectFilter.Months6,
                                    );
                              },
                            ),
                            const SizedBox(width: 10),
                            filterProjectWidget(
                              label: ProjectFilter.Custom,
                              groupValue: state.selectedFilter,
                              context: context,
                              onTap: () async {
                                final date = await showDateRangePicker(
                                  context: context,
                                  firstDate: DateTime(2023),
                                  lastDate: DateTime(2100),
                                );
                                if (date != null) {
                                  context
                                      .read<ProjectCubit>()
                                      .filterCompletedProjects(
                                        filterType: ProjectFilter.Custom,
                                        customRange: date,
                                      );
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 20),
                      Expanded(child: ProjectDetailTile(isMobile: true)),
                    ],
                  ),
                ),
              );
            },
          ),
          desktop: Scaffold(
            backgroundColor: AppColors.containerGreyColor,
            appBar: PreferredSize(
              child: CommonAppBar(),
              preferredSize: Size.fromHeight(60),
            ),
            body: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 160,
                vertical: 50,
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      InkWell(
                        onTap: () {
                          context.go(Routes.home);
                        },
                        child: Icon(Icons.arrow_back),
                      ),
                      SizedBox(width: 30),
                      Text("ALL PROJECTS", style: AppTextStyles.appBarHeading),
                    ],
                  ),
                  SizedBox(height: 30),
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 30, horizontal: 10),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      // border: Border.all(color: AppColors.borderGrey),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 15,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 30),
                          child: Row(
                            children: [
                              filterProjectWidget(
                                label: ProjectFilter.Monthly,
                                groupValue: state.selectedFilter,
                                context: context,
                                onTap: () {
                                  context
                                      .read<ProjectCubit>()
                                      .filterCompletedProjects(
                                        filterType: ProjectFilter.Monthly,
                                      );
                                },
                              ),

                              const SizedBox(width: 10),
                              filterProjectWidget(
                                label: ProjectFilter.Months6,
                                groupValue: state.selectedFilter,
                                context: context,
                                onTap: () {
                                  context
                                      .read<ProjectCubit>()
                                      .filterCompletedProjects(
                                        filterType: ProjectFilter.Months6,
                                      );
                                },
                              ),
                              const SizedBox(width: 10),
                              filterProjectWidget(
                                label: ProjectFilter.Custom,
                                groupValue: state.selectedFilter,
                                context: context,
                                onTap: () async {
                                  final date = await showDateRangePicker(
                                    context: context,
                                    firstDate: DateTime(2023),
                                    lastDate: DateTime(2100),
                                  );
                                  if (date != null) {
                                    context
                                        .read<ProjectCubit>()
                                        .filterCompletedProjects(
                                          filterType: ProjectFilter.Custom,
                                          customRange: date,
                                        );
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                        Divider(),
                        ProjectDetailTile(isMobile: false),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget filterProjectWidget({
    required String label,
    required String groupValue,
    required BuildContext context,
    VoidCallback? onTap,
  }) {
    final isSelected = label == groupValue;
    return GestureDetector(
      onTap:
          onTap ??
          () {
            context.read<ProjectCubit>().selectedFilter(label);
          },
      child: Container(
        height: 40,
        padding: const EdgeInsets.only(right: 20),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.secondary : Colors.transparent,
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Radio<String>(
              activeColor: AppColors.white,
              value: label,
              groupValue: groupValue,
              onChanged: (_) {
                if (onTap != null) {
                  onTap();
                } else {
                  context.read<ProjectCubit>().selectedFilter(label);
                }
              },
            ),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? AppColors.white : Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
