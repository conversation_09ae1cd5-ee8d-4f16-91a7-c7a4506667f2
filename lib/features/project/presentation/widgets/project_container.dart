import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/project/presentation/widgets/projectdetail_bottomsheet.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProjectDetailContainer extends StatelessWidget {
  ProjectDetailContainer({
    super.key,
    required this.isMobile,
    required this.project,
  });

  bool isMobile;
  final ProjectModel project;

  @override
  Widget build(BuildContext context) {
    final projectCubit = context.read<ProjectCubit>();
    return Container(
      padding: EdgeInsets.all(isMobile ? 15 : 30),
      decoration: BoxDecoration(
        color: AppColors.containerGreyColor,
        border: Border.all(color: AppColors.borderGrey),
        borderRadius: BorderRadius.circular(12),
      ),
      child:
          isMobile
              ? Column(
                children: [
                  // Main content
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Project image or fallback
                          project.projectImg != null
                              ? ClipRRect(
                                borderRadius: BorderRadius.circular(10),
                                child: CachedNetworkImage(
                                  imageUrl: project.projectImg ?? "",
                                  height: 60,
                                  width: 60,
                                  fit: BoxFit.cover,
                                  placeholder:
                                      (context, url) => Container(
                                        width: 60,
                                        height: 60,
                                        alignment: Alignment.center,
                                        child: const CircularProgressIndicator(
                                          color: AppColors.primary,
                                          strokeWidth: 2,
                                        ),
                                      ),
                                  errorWidget:
                                      (context, url, error) =>
                                          Container(width: 60, height: 60),
                                ),
                              )
                              : Container(
                                decoration: BoxDecoration(
                                  color: getColorFromInput(
                                    project.projectTitle[0],
                                  ),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                height: 60,
                                width: 60,
                                child: Center(
                                  child: Text(
                                    project.projectTitle[0].toUpperCase(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 30,
                                    ),
                                  ),
                                ),
                              ),
                          const SizedBox(width: 15),
                          // Title
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                project.projectTitle,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 8),

                              Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 3,
                                  horizontal: 8,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: statusColor(project.projectStatus),
                                ),
                                child: Text(
                                  project.projectStatus,
                                  style: const TextStyle(
                                    fontSize: 10,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      Text(project.projectDesc),
                    ],
                  ),
                ],
              )
              : Stack(
                children: [
                  // Main content
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Project image or fallback
                          project.projectImg != null
                              ? ClipRRect(
                                borderRadius: BorderRadius.circular(10),
                                child: CachedNetworkImage(
                                  imageUrl: project.projectImg ?? "",
                                  height: 60,
                                  width: 60,
                                  fit: BoxFit.cover,
                                  placeholder:
                                      (context, url) => Container(
                                        width: 60,
                                        height: 60,
                                        alignment: Alignment.center,
                                        child: const CircularProgressIndicator(
                                          color: AppColors.primary,
                                          strokeWidth: 2,
                                        ),
                                      ),
                                  errorWidget:
                                      (context, url, error) =>
                                          Container(width: 60, height: 60),
                                ),
                              )
                              : Container(
                                decoration: BoxDecoration(
                                  color: getColorFromInput(
                                    project.projectTitle[0],
                                  ),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                height: 60,
                                width: 60,
                                child: Center(
                                  child: Text(
                                    project.projectTitle[0].toUpperCase(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 30,
                                    ),
                                  ),
                                ),
                              ),
                          const SizedBox(width: 15),
                          // Title
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                project.projectTitle,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(truncateText(project.projectDesc, 37)),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      DetailRowTile(
                        title: "Supervisor ",
                        value:
                            projectCubit.getSupervisorName(
                              project.userId,
                              context,
                            ) ??
                            "",
                      ),
                      const SizedBox(height: 15),
                      DetailRowTile(
                        title: "Start Date",
                        value: project.createdAt?.goodDayDate() ?? "",
                      ),
                      const SizedBox(height: 15),
                      DetailRowTile(
                        title: "End Date",
                        value: project.completedAt?.goodDayDate() ?? "",
                      ),
                      const SizedBox(height: 15),
                      DetailRowTile(
                        title: "Total Users",
                        value: project.userId.length.toString(),
                      ),

                      // Row(
                      //   children: [Text("Supervisor"), Spacer(), Text("Matin")],
                      // ),
                    ],
                  ),

                  Positioned(
                    top: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 3,
                        horizontal: 8,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: statusColor(project.projectStatus),
                      ),
                      child: Text(
                        project.projectStatus,
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
    );
  }
}
