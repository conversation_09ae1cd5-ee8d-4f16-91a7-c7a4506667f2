import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfields.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/domain/entity/transcation_model.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_form_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_dropdown/multi_dropdown.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class ProjectForm extends StatefulWidget {
  ProjectModel? editProject;
  bool isMobile;
  ProjectForm({Key? key, required this.editProject, required this.isMobile})
    : super(key: key);

  @override
  State<ProjectForm> createState() => _ProjectFormState();
}

class _ProjectFormState extends State<ProjectForm> {
  final controller = MultiSelectController<String>();
  @override
  void initState() {
    context.read<ProjectFormCubit>().initializeForm(widget.editProject);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProjectFormCubit, ProjectFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        final projectFormCubit = context.read<ProjectFormCubit>();
        return Form(
          key: projectFormCubit.formKey,
          child: IgnorePointer(
            ignoring: state.isLoading,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Row(
                      children: [
                        IconButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          icon: Icon(CupertinoIcons.xmark),
                        ),
                        Text("Add Project", style: AppTextStyles.heading2),
                        Spacer(),
                        PrimaryButton(
                          isLoading: state.isLoading,
                          text: "Save",
                          onPressed: () {
                            projectFormCubit.submit(
                              widget.editProject,
                              context,
                            );
                          },

                          width: 100,
                          height: 36,
                        ),
                      ],
                    ),
                    SizedBox(height: 20),
                    StaggeredGrid.extent(
                      maxCrossAxisExtent: 400,
                      mainAxisSpacing: 15,
                      crossAxisSpacing: 30,
                      children: [
                        CustomTextField(
                          controller: projectFormCubit.titleController,
                          hintText: "project title",
                          title: "Title *",
                        ),
                        CustomTextField(
                          controller: projectFormCubit.descController,
                          hintText: "project description",
                          title: "Description *",
                        ),
                        CustomTextField(
                          controller: projectFormCubit.addressController,
                          hintText: "project address",
                          title: "Address *",
                        ),

                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Assign Users *"),
                            SizedBox(height: 5),
                            BlocBuilder<UserCubit, UserState>(
                              builder: (context, state) {
                                if (state.users.isNotEmpty) {
                                  final users = state.users;

                                  return MultiDropdown<String>(
                                    items: List.generate(users.length, (index) {
                                      return DropdownItem(
                                        label: users[index].name,
                                        value: users[index].docId,
                                        selected: projectFormCubit
                                            .state
                                            .selectedAssignUser
                                            .contains(users[index].docId),
                                        // selected:
                                      );
                                    }),
                                    controller: controller,
                                    enabled: true,
                                    chipDecoration: const ChipDecoration(
                                      backgroundColor: AppColors.secondary,
                                      labelStyle: TextStyle(
                                        color: AppColors.white,
                                      ),
                                      wrap: true,
                                      runSpacing: 10,
                                      spacing: 10,
                                    ),

                                    fieldDecoration: FieldDecoration(
                                      padding: EdgeInsets.all(10),
                                      // enabledBorder: OutlineInputBorder(
                                      //   borderSide: BorderSide(
                                      //     color: Colors.grey,
                                      //   ),
                                      //   borderRadius:
                                      //       BorderRadius.circular(10),
                                      // ),
                                      hintText: "assign to",
                                      prefixIcon: const Icon(
                                        CupertinoIcons.person_2,
                                      ),
                                      hintStyle: TextStyle(color: Colors.grey),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: BorderSide(
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ),
                                    dropdownDecoration:
                                        const DropdownDecoration(
                                          marginTop: 2,
                                          maxHeight: 500,
                                        ),
                                    dropdownItemDecoration:
                                        DropdownItemDecoration(
                                          selectedIcon: const Icon(
                                            Icons.check_box,
                                            color: Colors.green,
                                          ),
                                          disabledIcon: Icon(
                                            Icons.lock,
                                            color: Colors.grey.shade300,
                                          ),
                                        ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please select a assign user';
                                      }
                                      return null;
                                    },
                                    onSelectionChange: (selectedItems) {
                                      projectFormCubit.updateSelectedUser(
                                        selectedItems,
                                      );
                                    },
                                  );
                                }
                                return SizedBox();
                              },
                            ),
                          ],
                        ),

                        CustomDropDownField(
                          title: "Status *",
                          hintText: "project status",
                          initialValue: state.projectStatus,
                          validatorText: "Enter Project Status",
                          items: const [
                            DropdownMenuItem(
                              value: ProjectStatus.active,
                              child: Text(ProjectStatus.active),
                            ),
                            DropdownMenuItem(
                              value: ProjectStatus.finished,
                              child: Text(ProjectStatus.finished),
                            ),
                            DropdownMenuItem(
                              value: ProjectStatus.onHold,
                              child: Text(ProjectStatus.onHold),
                            ),
                          ],
                          onChanged: (value) {
                            projectFormCubit.selectStatus(value ?? "");
                          },
                        ),

                        CustomDateField(
                          hintText:
                              state.completedAt != null
                                  ? state.completedAt?.goodDayDate()
                                  : "project complete date",

                          title: "Completed At *",
                          onTap: () {
                            projectFormCubit.selectCompletedDate(context);
                          },
                          initialValue: state.completedAt?.goodDayDate() ?? "",
                        ),

                        CustomTextField(
                          controller: projectFormCubit.clientNameController,
                          hintText: "project client name",
                          title: "Client Name *",
                        ),
                        CustomNumTextField(
                          controller: projectFormCubit.clientContactController,
                          hintText: "project client contact",
                          title: "Client Contact *",
                        ),
                        CustomNumTextField(
                          controller: projectFormCubit.totalAmountController,
                          hintText: "project total amount",
                          title: "Total Amount *",
                        ),
                        CustomFileUploadField(
                          title: "Project Image",
                          hintText: "Upload Project Image",
                          onTap: () async => projectFormCubit.pickFile(context),
                          prefixIcon: Icon(CupertinoIcons.up_arrow),
                        ),

                        buildFilePreview(
                          context: context,
                          selectedFile: state.selectedFile,
                          dbFile: state.dbFile,
                          dbFileExt: widget.editProject?.projectImgExt,
                          dbFileName: widget.editProject?.projectImgName,
                          isEdit: true,
                          onDelete: () {
                            final isDbFile =
                                state.selectedFile == null &&
                                state.dbFile != null;
                            context.read<ProjectFormCubit>().deletPickFile(
                              isDbFile,
                            );
                          },
                          onView: () {
                            context.read<ProjectFormCubit>().viewPickFile(
                              state.dbFile,
                              context,
                              widget.editProject?.projectImgExt,
                            );
                          },
                          isMessage: false,
                        ),

                        widget.isMobile
                            ? CustomFileUploadField(
                              title: "",
                              hintText: "take new photo",
                              onTap:
                                  () async => projectFormCubit
                                      .pickFileFromCamera(context),
                              prefixIcon: Icon(CupertinoIcons.camera),
                            )
                            : SizedBox(height: 10),
                      ],
                    ),
                    Row(
                      children: [
                        Text("Transcations"),
                        widget.isMobile ? Spacer() : SizedBox(width: 10),
                        IconButton(
                          onPressed: () {
                            projectFormCubit.addTransaction();
                          },
                          icon: Icon(CupertinoIcons.add),
                        ),
                      ],
                    ),
                    state.transcations.isNotEmpty
                        ? StaggeredGrid.extent(
                          maxCrossAxisExtent: 400,
                          mainAxisSpacing: 15,
                          crossAxisSpacing: 30,
                          children: [
                            ...List.generate(state.transcations.length, (
                              index,
                            ) {
                              return TextFormField(
                                decoration: InputDecoration(
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color: Colors.grey,
                                    ), // Use your AppColors.grey2
                                  ),
                                  hintText: "add amount",
                                  hintStyle: TextStyle(
                                    color: Colors.grey,
                                  ), // Use your AppTextStyles.label
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  suffixIcon: IconButton(
                                    onPressed: () {
                                      projectFormCubit.removeTransaction(index);
                                    },
                                    icon: Icon(CupertinoIcons.delete),
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Enter amount';
                                  }
                                  if (double.tryParse(value) == null) {
                                    return 'Enter a valid number';
                                  }
                                  return null;
                                },
                                controller: TextEditingController(
                                  text:
                                      state.transcations[index].amount
                                          .toString(),
                                ),
                                onChanged: (value) {
                                  state.transcations[index].amount =
                                      num.tryParse(value) ?? 0;
                                },
                                keyboardType: TextInputType.number,
                              );
                            }),
                          ],
                        )
                        : SizedBox(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
