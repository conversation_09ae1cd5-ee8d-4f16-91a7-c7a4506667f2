import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_form_cubit.dart';
import 'package:cp_associates/features/project/presentation/widgets/project_form.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProjectDetailBottomsheet extends StatefulWidget {
  const ProjectDetailBottomsheet({super.key});

  @override
  State<ProjectDetailBottomsheet> createState() =>
      _ProjectDetailBottomsheetState();
}

class _ProjectDetailBottomsheetState extends State<ProjectDetailBottomsheet> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProjectCubit, ProjectState>(
      builder: (context, state) {
        if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        } else if (state.projectDetail != null) {
          final ProjectModel project = state.projectDetail!;

          num totalAmount = project.totalAmount;
          num transactionsTotal =
              project.transcations?.fold(0, (sum, t) => sum + t.amount) ?? 0;
          num per = totalAmount > 0 ? (transactionsTotal / totalAmount) : 0;
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: getColorFromInput(
                            state.projectDetail?.projectTitle[0] ?? "",
                          ),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        height: 30,
                        width: 30,
                        child: Center(
                          child: Text(
                            state.projectDetail?.projectTitle[0]
                                    .toUpperCase() ??
                                "",
                            style: TextStyle(color: Colors.white, fontSize: 12),
                          ),
                        ),
                      ),
                      SizedBox(width: 20),
                      Text(
                        state.projectDetail?.projectTitle ?? "",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Spacer(),
                      IconButton(
                        onPressed: () {
                          kIsWeb
                              ? showDialog(
                                context: context,
                                builder: (context) {
                                  return Dialog(
                                    child: Container(
                                      width: 800,
                                      child: BlocProvider(
                                        create:
                                            (context) => ProjectFormCubit(
                                              context.read<ProjectCubit>().repo,
                                            ),
                                        child: ProjectForm(
                                          editProject: project,
                                          isMobile: false,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              )
                              : {
                                Navigator.pop(context),
                                showModalBottomSheet(
                                  isScrollControlled: true,
                                  useSafeArea: true,
                                  context: context,
                                  builder: (context) {
                                    return Padding(
                                      padding:
                                          MediaQuery.of(context).viewInsets,
                                      child: BlocProvider(
                                        create:
                                            (context) => ProjectFormCubit(
                                              context.read<ProjectCubit>().repo,
                                            ),
                                        child: ProjectForm(
                                          editProject: project,
                                          isMobile: true,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              };
                        },
                        icon: Icon(CupertinoIcons.pen),
                      ),
                    ],
                  ),
                  SizedBox(height: 30),
                  Row(
                    children: [
                      Text(
                        "Status",
                        style: TextStyle(color: AppColors.grey2, fontSize: 14),
                      ),
                      Spacer(),
                      Container(
                        padding: EdgeInsets.all(5),

                        child: Text(
                          project.projectStatus,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: statusColor(project.projectStatus),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 30),
                  DetailRowTile(
                    title: "Start date",
                    value: project.createdAt?.goodDayDate(),
                  ),
                  SizedBox(height: 30),
                  DetailRowTile(
                    title: "Completion date(Estimated)",
                    value: project.completedAt?.goodDayDate(),
                  ),
                  SizedBox(height: 30),
                  DetailRowTile(
                    title: "Client name",
                    value: project.clientName,
                  ),
                  SizedBox(height: 30),
                  DetailRowTile(title: "Contact", value: project.clientContact),
                  SizedBox(height: 30),
                  Row(
                    children: [
                      Text(
                        "Payment",
                        style: TextStyle(color: AppColors.grey2, fontSize: 14),
                      ),
                      Spacer(),
                      Text("${(per * 100).toStringAsFixed(2)}%"),
                    ],
                  ),
                  SizedBox(height: 10),
                  Row(
                    children: [
                      Expanded(
                        child: Stack(
                          children: <Widget>[
                            Container(
                              decoration: BoxDecoration(
                                color: AppColors.borderGrey,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              width: MediaQuery.of(context).size.width,
                              height: 8,
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: AppColors.secondary,
                                borderRadius: BorderRadius.circular(10),
                              ),

                              width:
                                  (MediaQuery.of(context).size.width *
                                      per), // here you can define your percentage of progress, 0.2 = 20%, 0.3 = 30 % .....
                              height: 8,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                ],
              ),
            ),
          );
        } else {
          return Center(child: Text("Project detail not found"));
        }
      },
    );
  }
}

class DetailRowTile extends StatelessWidget {
  const DetailRowTile({super.key, required this.title, required this.value});

  final String title;
  final String? value;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(title, style: TextStyle(color: AppColors.grey2, fontSize: 14)),
        Spacer(),
        Text(value ?? "", style: TextStyle(fontWeight: FontWeight.w600)),
      ],
    );
  }
}
