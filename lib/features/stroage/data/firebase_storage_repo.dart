import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/features/stroage/domain/storage_repo.dart';
import 'package:flutter/cupertino.dart';

import '../../../core/utils/helpers.dart';

class FirebaseStorageRepo extends StorageRepo {
  Future<String?> uploadBillsFile(SelectedImage imageFile) async {
    try {
      final imageRef = FBStorage.bills.child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extension}',
      );
      final task = await imageRef.putData(
        imageFile.uInt8List,
        metaDataGenerator(imageFile),
      );
      return await task.ref.getDownloadURL();
    } on Exception catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }

  Future<String?> uploadProjectImg(SelectedImage imageFile) async {
    try {
      final imageRef = FBStorage.project.child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extension}',
      );
      final task = await imageRef.putData(
        imageFile.uInt8List,
        metaDataGenerator(imageFile),
      );
      return await task.ref.getDownloadURL();
    } on Exception catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }

  Future<String?> uploadDocumentsFile(SelectedImage imageFile) async {
    try {
      final imageRef = FBStorage.documents.child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extension}',
      );
      final task = await imageRef.putData(
        imageFile.uInt8List,
        metaDataGenerator(imageFile),
      );
      return await task.ref.getDownloadURL();
    } on Exception catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }

  Future<String?> uploadTaskFile(SelectedImage imageFile) async {
    try {
      final imageRef = FBStorage.tasks.child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extension}',
      );
      final task = await imageRef.putData(
        imageFile.uInt8List,
        metaDataGenerator(imageFile),
      );
      return await task.ref.getDownloadURL();
    } on Exception catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }

  Future<String?> uploadActivityFile(SelectedImage imageFile) async {
    try {
      final imageRef = FBStorage.activity.child(
        '${DateTime.now().millisecondsSinceEpoch}.${imageFile.extension}',
      );
      final task = await imageRef.putData(
        imageFile.uInt8List,
        metaDataGenerator(imageFile),
      );
      return await task.ref.getDownloadURL();
    } on Exception catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }
}
