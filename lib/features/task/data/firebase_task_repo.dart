import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/task/domain/entity/task_logs_model.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';
import 'package:cp_associates/features/task/domain/repo/task_repo.dart';

class FirebaseTaskRepo extends TaskRepo {
  final taskRef = FBFireStore.tasks;
  @override
  Future<String> createTask(TaskModel task) async {
    final ref = taskRef.doc();
    final newTask = task.copyWith(docId: ref.id);
    await ref.set(newTask.toJson());
    return ref.id;
  }

  @override
  Future<void> updateTask(TaskModel task) async {
    await taskRef.doc(task.docId).update(task.toJson());
  }

  @override
  Future<void> deleteTask(String taskId) async {
    await taskRef.doc(taskId).delete();
  }

  @override
  Stream<List<TaskModel>> fetchProjectTask(String projectId) {
    return taskRef
        .where("projectId", isEqualTo: projectId)
        .orderBy("createdAt", descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => TaskModel.fromSnapshot(doc))
              .toList();
        });
  }

  @override
  Stream<TaskModel> fetchTaskById(String taskId) {
    return taskRef
        .doc(taskId)
        .snapshots()
        .map((doc) => TaskModel.fromJson(doc.data() as Map<String, dynamic>));
  }

  @override
  Stream<List<TaskLogsModel>> fetchTaskLogs(String taskId) {
    return taskRef
        .doc(taskId)
        .collection("tasklogs")
        .orderBy("createdAt", descending: false)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) => TaskLogsModel.fromSnapshot(doc))
              .toList();
        });
  }
}
