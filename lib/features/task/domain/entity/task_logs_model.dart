import 'package:cloud_firestore/cloud_firestore.dart';

class TaskLogsModel {
  final String taskId;
  final DateTime createdAt;
  final String createdBy;
  final String status;

  TaskLogsModel({
    required this.taskId,
    required this.createdAt,
    required this.createdBy,
    required this.status,
  });

  TaskLogsModel copyWith({
    String? taskId,
    DateTime? createdAt,
    String? createdBy,
    String? status,
  }) {
    return TaskLogsModel(
      taskId: taskId ?? this.taskId,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      status: status ?? this.status,
    );
  }

  /// Firestore snapshot deserialization
  factory TaskLogsModel.fromSnapshot(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;

    return TaskLogsModel(
      taskId: snap.id,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      status: data['status'] ?? '',
    );
  }

  /// JSON deserialization
  factory TaskLogsModel.fromJson(Map<String, dynamic> json) {
    return TaskLogsModel(
      taskId: json['taskId'] ?? '',
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      createdBy: json['createdBy'] ?? '',
      status: json['status'] ?? '',
    );
  }

  /// JSON serialization
  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'status': status,
    };
  }

  /// Map without ID (for adding to Firestore)
  Map<String, dynamic> toMap() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'status': status,
    };
  }
}
