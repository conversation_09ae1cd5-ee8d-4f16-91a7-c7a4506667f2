import 'package:cloud_firestore/cloud_firestore.dart';

class TaskModel {
  final String docId;
  final DateTime createdAt;
  final String createdBy;
  final String priority;
  final String desc;
  final String status;
  final String projectId;
  final DateTime? startDate;
  final DateTime endDate;
  final String assignTo;
  final String title;
  final String? attachments;
  final String? attachmentType;
  final String? attachmentName;

  TaskModel({
    required this.docId,
    required this.createdAt,
    required this.createdBy,
    required this.priority,
    required this.desc,
    required this.status,
    required this.projectId,
    required this.startDate,
    required this.endDate,
    required this.assignTo,
    required this.title,
    required this.attachments,
    required this.attachmentType,
    required this.attachmentName,
  });

  // copyWith method
  TaskModel copyWith({
    String? docId,
    DateTime? createdAt,
    String? createdBy,
    String? priority,
    String? desc,
    String? status,
    String? projectId,
    DateTime? startDate,
    DateTime? endDate,
    // List<String>? assignTo,
    String? assignTo,
    String? title,
    String? attachments,
    String? attachmentType,
    String? attachmentName,
  }) {
    return TaskModel(
      docId: docId ?? this.docId,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      priority: priority ?? this.priority,
      desc: desc ?? this.desc,
      status: status ?? this.status,
      projectId: projectId ?? this.projectId,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      // assignTo: assignTo ?? this.assignTo,
      assignTo: assignTo ?? this.assignTo,
      title: title ?? this.title,
      attachments: attachments ?? this.attachments,
      attachmentType: attachmentType ?? this.attachmentType,
      attachmentName: attachmentName ?? this.attachmentName,
    );
  }

  /// Firestore snapshot to TaskModel
  factory TaskModel.fromSnapshot(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return TaskModel(
      docId: snap.id,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      priority: data['priority'] ?? '',
      desc: data['desc'] ?? '',
      status: data['status'] ?? '',
      projectId: data['projectId'] ?? '',
      startDate:
          data['startDate'] != null
              ? (data['startDate'] as Timestamp).toDate()
              : null,
      endDate: (data['endDate'] as Timestamp).toDate(),
      // assignTo: List<String>.from(data['assignTo'] ?? []),
      assignTo: data['assignTo'] ?? '',
      title: data['title'] ?? '',
      attachments: data['attachments'],
      attachmentType: data['attachmentType'],
      attachmentName: data['attachmentName'],
    );
  }

  /// JSON to TaskModel
  factory TaskModel.fromJson(Map<String, dynamic> json) {
    return TaskModel(
      docId: json['docId'] ?? '',
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      createdBy: json['createdBy'] ?? '',
      priority: json['priority'] ?? '',
      desc: json['desc'] ?? '',
      status: json['status'] ?? '',
      projectId: json['projectId'] ?? '',
      startDate:
          json['startDate'] != null
              ? (json['startDate'] as Timestamp).toDate()
              : null,
      endDate: (json['endDate'] as Timestamp).toDate(),
      assignTo: json['assignTo'] ?? '',
      title: json['title'] ?? '',
      attachments: json['attachments'] ?? null,
      attachmentType: json['attachmentType'] ?? null,
      attachmentName: json['attachmentName'] ?? null,
    );
  }

  /// Convert TaskModel to Firestore map (for upload/update)
  Map<String, dynamic> toMap() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'priority': priority,
      'desc': desc,
      'status': status,
      'projectId': projectId,
      'startDate': startDate != null ? Timestamp.fromDate(startDate!) : null,
      'endDate': Timestamp.fromDate(endDate),
      // 'assignTo': assignTo,
      'assignTo': assignTo,
      'title': title,
      'attachments': attachments,
      'attachmentType': attachmentType,
    };
  }

  /// Convert TaskModel to JSON (for local storage/API)
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'createdAt': createdAt,
      'createdBy': createdBy,
      'priority': priority,
      'desc': desc,
      'status': status,
      'projectId': projectId,
      'startDate': startDate,
      'endDate': endDate,
      // 'assignTo': assignTo,
      'assignTo': assignTo,
      'title': title,
      'attachments': attachments,
      'attachmentType': attachmentType,
      'attachmentName': attachmentName,
    };
  }
}
