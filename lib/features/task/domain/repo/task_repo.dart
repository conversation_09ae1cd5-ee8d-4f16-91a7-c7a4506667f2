import 'package:cp_associates/features/task/domain/entity/task_logs_model.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';

abstract class TaskRepo {
  Future<String> createTask(TaskModel task);
  Future<void> updateTask(TaskModel task);
  Future<void> deleteTask(String taskId);
  Stream<List<TaskModel>> fetchProjectTask(String projectId);
  Stream<TaskModel> fetchTaskById(String taskId);
  Stream<List<TaskLogsModel>> fetchTaskLogs(String taskId);
}
