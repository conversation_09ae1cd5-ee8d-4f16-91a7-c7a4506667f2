import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/task/domain/entity/task_logs_model.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';
import 'package:cp_associates/features/task/domain/repo/task_repo.dart';

part 'task_state.dart';

class TaskCubit extends Cubit<TaskState> {
  TaskRepo taskRepo;
  TaskCubit(this.taskRepo) : super(TaskState.initial());

  StreamSubscription<List<TaskModel>>? taskStream;
  StreamSubscription<List<TaskLogsModel>>? _taskLogsSubscription;

  //ui related functions
  void filterTaskByType(String type) {
    emit(state.copyWith(isLoading: true, message: ""));
    if (type == TaskTypes.All) {
      emit(
        state.copyWith(
          task: state.task,
          selectedType: TaskTypes.All,
          isLoading: false,
          message: "",
        ),
      );
    } else if (type == TaskTypes.mytask) {
      emit(state.copyWith(selectedType: type, isLoading: false, message: ""));
    } else if (type == TaskTypes.submitted) {
      emit(state.copyWith(selectedType: type, isLoading: false, message: ""));
    } else if (type == TaskTypes.approved) {
      emit(state.copyWith(selectedType: type, isLoading: false, message: ""));
    } else {
      emit(state.copyWith(selectedType: type, isLoading: false, message: ""));
    }
  }

  // task related functions
  fetchAllTask(String projectId) {
    print("TaskStream------");
    emit(state.copyWith(task: [], isLoading: true, message: ""));
    taskStream?.cancel();
    taskStream = taskRepo
        .fetchProjectTask(projectId)
        .listen(
          (task) {
            print("TaskStream------2");
            emit(state.copyWith(task: task, isLoading: false, message: ""));
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch bills: ${error.toString()}",
              ),
            );
          },
        );
  }

  fetchTaskById(String taskId) async {
    emit(state.copyWith(taskDetail: null, isLoading: true, message: ""));
    await taskRepo
        .fetchTaskById(taskId)
        .listen(
          (task) {
            emit(
              state.copyWith(isLoading: false, taskDetail: task, message: ""),
            );
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch task: ${error.toString()}",
              ),
            );
          },
        );
  }

  deletTaskLog(String taskId) async {
    final taskLogs =
        await FBFireStore.tasks.doc(taskId).collection("tasklogs").get();

    for (final doc in taskLogs.docs) {
      await doc.reference.delete();
    }
  }

  deletTask(String taskId) async {
    emit(state.copyWith(isLoading: true, message: ""));
    deletTaskLog(taskId);
    await taskRepo.deleteTask(taskId);
    emit(
      state.copyWith(isLoading: false, message: "Task deleted succesfully."),
    );
  }

  // task logs related functions
  void fetchTaskLogs(String taskId) {
    print("TaskLogsStream------");
    emit(state.copyWith(isLoading: true, taskLogs: []));
    _taskLogsSubscription?.cancel();

    _taskLogsSubscription = taskRepo
        .fetchTaskLogs(taskId)
        .listen(
          (taskLogs) {
            print("TaskLogsStream------2");
            emit(state.copyWith(isLoading: false, taskLogs: taskLogs));
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch task logs: ${error.toString()}",
              ),
            );
          },
        );
  }

  String? displayCompletionStatus(TaskModel task) {
    final now = DateTime.now();

    if (task.status == TaskStatus.submitted ||
        task.status == TaskStatus.approved) {
      final submittedLogs =
          state.taskLogs
              .where((log) => log.status == TaskStatus.submitted)
              .toList()
            ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

      if (submittedLogs.isNotEmpty) {
        return "${submittedLogs.first.createdAt.goodDayDate()}";
      }
    }

    if (now.isAfter(task.endDate)) {
      return "Overdue";
    }

    return null;
  }
}
