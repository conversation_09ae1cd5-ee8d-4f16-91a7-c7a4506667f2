part of 'task_cubit.dart';

class TaskState {
  final List<TaskModel> task;
  final List<TaskLogsModel> taskLogs;
  final String selectedType;
  final TaskModel? taskDetail;
  final String message;
  final bool isLoading;
  final bool btnLoading;

  TaskState({
    required this.task,
    required this.taskLogs,
    required this.taskDetail,
    required this.selectedType,
    required this.message,
    required this.isLoading,
    required this.btnLoading,
  });

  factory TaskState.initial() {
    return TaskState(
      task: [],
      taskLogs: [],
      taskDetail: null,
      selectedType: 'All',
      message: '',
      isLoading: false,
      btnLoading: false,
    );
  }

  TaskState copyWith({
    List<TaskModel>? task,
    List<TaskLogsModel>? taskLogs,
    final TaskModel? taskDetail,
    String? selectedType,
    String? message,
    bool? isLoading,
    bool? btnLoading,
  }) {
    return TaskState(
      taskLogs: taskLogs ?? this.taskLogs,
      task: task ?? this.task,
      taskDetail: taskDetail ?? this.taskDetail,
      selectedType: selectedType ?? this.selectedType,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      btnLoading: btnLoading ?? this.btnLoading,
    );
  }
}
