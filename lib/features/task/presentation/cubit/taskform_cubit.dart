import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/mastertask/domain/entity/master_task_model.dart';
import 'package:cp_associates/features/stroage/data/firebase_storage_repo.dart';
import 'package:cp_associates/features/task/domain/entity/task_logs_model.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';
import 'package:cp_associates/features/task/domain/repo/task_repo.dart';
import 'package:flutter/material.dart';
part 'taskform_state.dart';

class TaskFormCubit extends Cubit<TaskFormState> {
  TaskRepo taskRepo;
  TaskFormCubit(this.taskRepo) : super(TaskFormState.initial());

  final titleController = TextEditingController();
  final descController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  void initializeForm(TaskModel? editTask) {
    if (editTask != null) {
      titleController.text = editTask.title;
      descController.text = editTask.desc;

      emit(
        state.copyWith(
          priorityStatus: editTask.priority,
          taskStatus: editTask.status,
          startDate: editTask.startDate,
          endDate: editTask.endDate,
          selectedUser: editTask.assignTo,
          dbFile: editTask.attachments,
        ),
      );
    } else {
      emit(TaskFormState.initial());
    }
  }

  Future<void> pickFile(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickFile(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  Future<void> pickFileFromCamera(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickImageNewCamera(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  Future<void> viewPickFile(
    String? dbImg,
    BuildContext context,
    String? dbImgExt,
  ) async {
    await viewFile(
      context: context,
      selectedFile: state.selectedFile,
      dbImg: dbImg,
      dbImgExt: dbImgExt,
    );
  }

  void deletPickFile(bool dbImage) {
    if (dbImage) {
      emit(state.copyWith(dbFile: false));
    } else {
      emit(state.copyWith(selectedFile: false));
    }
  }

  void priorityStatus(String priorityStatus) {
    if (priorityStatus.toLowerCase() == TaskSPrioritytatus.high.toLowerCase()) {
      emit(state.copyWith(priorityStatus: priorityStatus));
    } else if (priorityStatus.toLowerCase() ==
        TaskSPrioritytatus.mid.toLowerCase()) {
      emit(state.copyWith(priorityStatus: priorityStatus));
    } else {
      emit(state.copyWith(priorityStatus: priorityStatus));
    }
  }

  void selectStatus(String status) {
    if (status.toLowerCase() == TaskStatus.submitted.toLowerCase()) {
      emit(state.copyWith(taskStatus: status));
    } else if (status.toLowerCase() == TaskStatus.ongoing.toLowerCase()) {
      emit(state.copyWith(taskStatus: status));
    } else {
      emit(state.copyWith(taskStatus: status));
    }
  }

  void createStatusLog(String status, String taskId, String userId) async {
    final tasklog = TaskLogsModel(
      taskId: taskId,
      createdAt: DateTime.now(),
      createdBy: userId,
      status: status,
    );

    await FBFireStore.tasks.doc(tasklog.taskId).update({"status": status});
    await FBFireStore.tasks
        .doc(tasklog.taskId)
        .collection("tasklogs")
        .add(tasklog.toJson());

    if (!isClosed) {
      emit(
        state.copyWith(
          taskStatus: status,
          message: "Status updated to $status",
        ),
      );
    }

    // emit(
    //   state.copyWith(taskStatus: status, message: "status update to ${status}"),
    // );
  }

  void selectStartDate(BuildContext context) async {
    final res = await showDatePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime(2026),
    );

    emit(state.copyWith(startDate: res));
  }

  void selectEndtDate(BuildContext context) async {
    final res = await showDatePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime(2026),
    );

    emit(state.copyWith(endDate: res));
  }

  void updateSelectedUser(String userId) {
    emit(state.copyWith(selectedUser: userId));
  }

  void submit(
    TaskModel? editTask,
    String projectId,
    BuildContext context,
  ) async {
    if (state.isLoading) {
      return;
    }
    if (formKey.currentState?.validate() ?? false) {
      emit(state.copyWith(isLoading: true, message: ''));
      try {
        //check file is from upload or database
        final fileUrl =
            state.selectedFile != null
                ? await FirebaseStorageRepo().uploadTaskFile(
                  state.selectedFile!,
                )
                : state.dbFile;

        //assing data
        final task = TaskModel(
          docId: editTask?.docId ?? "",
          createdAt: editTask?.createdAt ?? DateTime.now(),
          createdBy: FBAuth.auth.currentUser?.uid ?? "",
          priority: state.priorityStatus,
          desc: descController.text,
          status: state.taskStatus ?? "",
          projectId: projectId,
          startDate: editTask?.startDate ?? state.startDate,
          endDate: editTask?.endDate ?? state.endDate ?? DateTime.now(),
          assignTo: state.selectedUser,
          title: titleController.text,
          attachments: fileUrl,
          attachmentType:
              state.selectedFile?.extension ?? editTask?.attachmentType ?? "",
          attachmentName:
              state.selectedFile?.name ?? editTask?.attachmentName ?? "",
        );

        //if new task created
        if (editTask == null) {
          final taskId = await taskRepo.createTask(task);
          createStatusLog(
            task.status,
            taskId,
            FBAuth.auth.currentUser?.uid ?? "",
          );
          Navigator.of(context).pop();
          emit(
            state.copyWith(
              isLoading: false,
              message: "New Task created successfully",
            ),
          );
        } else {
          final oldStatus = editTask.status;
          final newStatus = task.status;

          await taskRepo.updateTask(task);

          if (oldStatus.toLowerCase() != newStatus.toLowerCase()) {
            createStatusLog(
              newStatus,
              task.docId,
              FBAuth.auth.currentUser?.uid ?? "",
            );
          }
          Navigator.of(context).pop();
          emit(
            state.copyWith(
              isLoading: false,
              message: "Update Task successfully",
            ),
          );
        }
      } catch (e) {
        emit(state.copyWith(isLoading: false, message: e.toString()));
      }
    }
  }

  void selectMasterTask(MasterTaskModel masterTask) {
    emit(state.copyWith(masterTaskId: masterTask.docId));
    if (masterTask.docId.isNotEmpty) {
      titleController.text = masterTask.title;
      descController.text = masterTask.desc;

      emit(
        state.copyWith(
          selectedUser: masterTask.assignTo ?? '',
          startDate: DateTime.now(),
          endDate: DateTime.now().add(Duration(days: masterTask.duration)),
        ),
      );
    }
  }
}
