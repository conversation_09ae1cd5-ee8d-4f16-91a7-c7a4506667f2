part of 'taskform_cubit.dart';

class TaskFormState {
  final String message;
  final bool isLoading;
  SelectedImage? selectedFile;
  final String? dbFile;
  // final List<String> selectedAssignUser;
  String selectedUser;
  final String? taskStatus;
  final String priorityStatus;
  final DateTime? startDate;
  final DateTime? endDate;
  String? masterTaskId;

  TaskFormState({
    required this.message,
    required this.isLoading,
    required this.selectedFile,
    required this.dbFile,
    // required this.selectedAssignUser,
    required this.selectedUser,
    required this.taskStatus,
    required this.priorityStatus,
    required this.startDate,
    required this.endDate,
    required this.masterTaskId,
  });
  factory TaskFormState.initial() {
    return TaskFormState(
      message: '',
      isLoading: false,
      selectedFile: null,
      dbFile: null,
      // selectedAssignUser: [],
      selectedUser: '',
      taskStatus: null,
      priorityStatus: '',
      startDate: null,
      endDate: null,
      masterTaskId: null,
    );
  }

  TaskFormState copyWith({
    String? message,
    bool? isLoading,
    dynamic selectedFile,
    dynamic dbFile,
    // List<String>? selectedAssignUser,
    String? selectedUser,
    String? taskStatus,
    String? priorityStatus,
    DateTime? startDate,
    DateTime? endDate,
    String? masterTaskId,
  }) {
    return TaskFormState(
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      selectedFile:
          selectedFile is bool ? null : (selectedFile ?? this.selectedFile),
      dbFile: dbFile is bool ? null : (dbFile ?? this.dbFile),
      // selectedAssignUser: selectedAssignUser ?? this.selectedAssignUser,
      selectedUser: selectedUser ?? this.selectedUser,
      taskStatus: taskStatus ?? this.taskStatus,
      priorityStatus: priorityStatus ?? this.priorityStatus,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      masterTaskId: masterTaskId ?? this.masterTaskId,
    );
  }
}
