import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfields.dart';
import 'package:cp_associates/core/widgets/filter_container.dart';
import 'package:cp_associates/features/mastertask/cubit/master_task_cubit.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';
import 'package:cp_associates/features/task/presentation/cubit/taskform_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_dropdown/multi_dropdown.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class TaskForm extends StatefulWidget {
  TaskModel? editTask;
  String projectId;
  bool isMobile;
  TaskForm({
    super.key,
    required this.projectId,
    required this.editTask,
    required this.isMobile,
  });

  @override
  State<TaskForm> createState() => _TaskFormState();
}

class _TaskFormState extends State<TaskForm> {
  final controller = MultiSelectController<String>();

  @override
  void initState() {
    context.read<TaskFormCubit>().initializeForm(widget.editTask);
    context.read<MasterTaskCubit>().fetchAllMasterTask();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final taskFormCubit = context.read<TaskFormCubit>();
    return BlocBuilder<TaskFormCubit, TaskFormState>(
      builder: (context, state) {
        return IgnorePointer(
          ignoring: state.isLoading,
          child: SingleChildScrollView(
            child: Form(
              key: taskFormCubit.formKey,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Row(
                      children: [
                        IconButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          icon: Icon(CupertinoIcons.xmark),
                        ),
                        Text("Add Task", style: AppTextStyles.formtitle),
                        Spacer(),
                        PrimaryButton(
                          isLoading: state.isLoading,
                          text: "Save",
                          onPressed: () {
                            taskFormCubit.submit(
                              widget.editTask,
                              widget.projectId,
                              context,
                            );
                          },

                          width: 100,
                          height: 36,
                        ),
                      ],
                    ),
                    SizedBox(height: 10),
                    StaggeredGrid.extent(
                      maxCrossAxisExtent: 400,
                      mainAxisSpacing: 15,
                      crossAxisSpacing: 30,
                      children: [
                        CustomDropDownField(
                          title: "Master Task",
                          hintText: "select master task",
                          initialValue: state.masterTaskId,
                          validatorText: "Enter Master Task",
                          items:
                              context
                                  .watch<MasterTaskCubit>()
                                  .state
                                  .masterTask
                                  .map((task) {
                                    return DropdownMenuItem<String>(
                                      value: task.docId,
                                      child: Text(task.title),
                                    );
                                  })
                                  .toList(),
                          onChanged: (value) {
                            final masterTask = context
                                .read<MasterTaskCubit>()
                                .state
                                .masterTask
                                .firstWhere((task) => task.docId == value);
                            taskFormCubit.selectMasterTask(masterTask);
                          },
                        ),

                        // Divider(thickness: 1, color: Colors.grey),
                        CustomTextField(
                          controller: taskFormCubit.titleController,
                          hintText: "task name",
                          title: "Title *",
                        ),

                        CustomTextField(
                          controller: taskFormCubit.descController,
                          hintText: "task desc",
                          title: "Description *",
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Assign Users *"),
                            SizedBox(height: 5),
                            BlocBuilder<UserCubit, UserState>(
                              builder: (context, userState) {
                                if (userState.users.isNotEmpty) {
                                  final users = userState.users;

                                  return DropdownButtonFormField<String>(
                                    value:
                                        state.selectedUser.isEmpty
                                            ? null
                                            : state.selectedUser,
                                    decoration: InputDecoration(
                                      hintText: "Assign to",
                                      border: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: AppColors.grey2,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: BorderSide(
                                          color: AppColors.grey2,
                                        ),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: BorderSide(
                                          color: AppColors.grey2,
                                        ),
                                      ),
                                    ),
                                    items:
                                        users.map((user) {
                                          return DropdownMenuItem<String>(
                                            value: user.docId,
                                            child: Text(user.name),
                                          );
                                        }).toList(),
                                    onChanged: (value) {
                                      if (value != null) {
                                        taskFormCubit.updateSelectedUser(value);
                                      }
                                    },
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please select a user';
                                      }
                                      return null;
                                    },
                                  );
                                } else {
                                  return Text("No users available");
                                }
                              },
                            ),
                          ],
                        ),

                        CustomDateField(
                          hintText:
                              state.startDate != null
                                  ? state.startDate?.goodDayDate()
                                  : "start date",
                          title: "Start Date *",
                          onTap: () {
                            taskFormCubit.selectStartDate(context);
                          },
                          initialValue: state.startDate?.goodDayDate() ?? "",
                        ),

                        CustomDateField(
                          hintText:
                              state.endDate != null
                                  ? state.endDate?.goodDayDate()
                                  : "end date",
                          title: "End Date *",
                          onTap: () {
                            taskFormCubit.selectEndtDate(context);
                          },
                          initialValue: state.endDate?.goodDayDate() ?? "",
                        ),

                        CustomDropDownField(
                          title: "Status *",
                          hintText: "task status",
                          initialValue: state.taskStatus,
                          validatorText: "Enter Task Status",
                          items: const [
                            DropdownMenuItem(
                              value: TaskStatus.pending,
                              child: Text(TaskStatus.pending),
                            ),
                            DropdownMenuItem(
                              value: TaskStatus.ongoing,
                              child: Text(TaskStatus.ongoing),
                            ),
                            DropdownMenuItem(
                              value: TaskStatus.submitted,
                              child: Text(TaskStatus.submitted),
                            ),
                            DropdownMenuItem(
                              value: TaskStatus.approved,
                              child: Text(TaskStatus.approved),
                            ),
                          ],
                          onChanged: (value) {
                            taskFormCubit.selectStatus(value ?? "");
                          },
                        ),

                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Attachments"),
                            buildFilePreview(
                              context: context,
                              selectedFile: state.selectedFile,
                              dbFile: state.dbFile,
                              dbFileExt: widget.editTask?.attachmentType,
                              dbFileName: widget.editTask?.attachmentName,
                              isEdit: true,
                              onDelete: () {
                                final isDbFile =
                                    state.selectedFile == null &&
                                    state.dbFile != null;
                                context.read<TaskFormCubit>().deletPickFile(
                                  isDbFile,
                                );
                              },
                              onView: () {
                                context.read<TaskFormCubit>().viewPickFile(
                                  state.dbFile,
                                  context,
                                  widget.editTask?.attachmentType,
                                );
                              },
                              isMessage: false,
                            ),

                            CustomFileUploadField(
                              title: "",
                              onTap: () async {
                                taskFormCubit.pickFile(context);
                              },
                              hintText: "Upload Document (image, pdf) *",
                              prefixIcon: Icon(Icons.attach_file),
                            ),

                            kIsWeb
                                ? SizedBox(height: 10)
                                : CustomFileUploadField(
                                  title: "",
                                  hintText: "Take new photo",
                                  onTap: () async {
                                    taskFormCubit.pickFileFromCamera(context);
                                  },
                                  prefixIcon: Icon(CupertinoIcons.camera),
                                ),
                          ],
                        ),

                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Priority Status *"),
                            SizedBox(height: 5),
                            Row(
                              children: [
                                FilterContainer(
                                  title: "Mid",
                                  onFilterTap: () {
                                    taskFormCubit.priorityStatus(
                                      TaskSPrioritytatus.mid,
                                    );
                                  },
                                  isSelected:
                                      state.priorityStatus ==
                                      TaskSPrioritytatus.mid,
                                ),
                                SizedBox(width: 10),
                                FilterContainer(
                                  title: "High",
                                  onFilterTap: () {
                                    taskFormCubit.priorityStatus(
                                      TaskSPrioritytatus.high,
                                    );
                                  },
                                  isSelected:
                                      state.priorityStatus ==
                                      TaskSPrioritytatus.high,
                                ),
                                SizedBox(width: 10),
                                FilterContainer(
                                  title: "Low",
                                  onFilterTap: () {
                                    taskFormCubit.priorityStatus(
                                      TaskSPrioritytatus.low,
                                    );
                                  },
                                  isSelected:
                                      state.priorityStatus ==
                                      TaskSPrioritytatus.low,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
