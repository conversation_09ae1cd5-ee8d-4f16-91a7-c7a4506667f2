import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';
import 'package:cp_associates/features/task/presentation/cubit/task_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TaskHeaderDetail extends StatefulWidget {
  const TaskHeaderDetail({
    super.key,
    //  required this.user,
    required this.task,
  });

  // final UserModel? user;
  final TaskModel? task;

  @override
  State<TaskHeaderDetail> createState() => _TaskHeaderDetailState();
}

class _TaskHeaderDetailState extends State<TaskHeaderDetail> {
  @override
  void initState() {
    context.read<TaskCubit>().fetchTaskLogs(widget.task?.docId ?? "");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final userCubit = context.read<UserCubit>();

    final user = userCubit.getUserById(widget.task?.assignTo ?? "");
    final taskCubit = context.read<TaskCubit>();

    final completionStatus = taskCubit.displayCompletionStatus(widget.task!);

    return Row(
      // crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(15),
          child: Text(
            user?.name[0].toUpperCase() ?? '',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppColors.secondary,
          ),
        ),
        SizedBox(width: 15),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(user?.name ?? ""),
            Row(
              children: [
                Text(
                  widget.task?.status ?? "",
                  style: TextStyle(fontSize: 12, color: AppColors.grey2),
                ),
                if (completionStatus != null) ...[
                  SizedBox(width: 5),
                  Icon(Icons.circle, size: 5, color: AppColors.grey2),
                  SizedBox(width: 5),
                  Text(
                    completionStatus,
                    style: TextStyle(
                      fontSize: 12,
                      color:
                          completionStatus.startsWith("Overdue")
                              ? Colors.red
                              : AppColors.grey2,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        Spacer(),
        Container(
          padding: EdgeInsets.all(5),

          child: Text(
            widget.task?.priority ?? "",
            style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: priorityStatusColor(widget.task?.priority ?? ''),
          ),
        ),
      ],
    );
  }
}
