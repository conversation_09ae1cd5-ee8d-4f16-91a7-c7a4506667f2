import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/auth/domain/repo/auth_repo.dart';
import 'package:cp_associates/features/users/domain/repo/user_repo.dart';
import 'package:flutter/material.dart';
import 'package:meta/meta.dart';

part 'user_state.dart';

class UserCubit extends Cubit<UserState> {
  final UserRepo userRepo;
  final AuthRepo authRepo;
  StreamSubscription<List<UserModel>>? userStream;
  UserCubit(this.userRepo, this.authRepo) : super(UserState.initial());

  void fetchAllUsers() {
    userStream?.cancel();

    emit(state.copyWith(isLoading: true, message: "", users: []));

    userStream = userRepo.getAllUsers().listen(
      (users) {
        emit(state.copyWith(users: users, isLoading: false));
        print("UsersStream------${users.length}");
      },
      onError: (e) {
        emit(
          state.copyWith(
            message: "Failed to load Users: ${e.toString()}",
            isLoading: false,
          ),
        );
      },
    );
  }

  void deleteUser(String userId) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await userRepo.deleteUser(userId);
      emit(
        state.copyWith(isLoading: false, message: "User deleted successfully"),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to delete user: ${e.toString()}",
        ),
      );
    }
  }

  UserModel? getUserById(String userId) {
    try {
      final user = state.users.firstWhere((user) => user.docId == userId);
      return user;
    } catch (_) {
      return null;
    }
  }
}
