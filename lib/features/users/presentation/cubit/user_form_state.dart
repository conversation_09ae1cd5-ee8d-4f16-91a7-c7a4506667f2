part of 'user_form_cubit.dart';

class UserFormState {
  final String message;
  final bool isLoading;
  final String selectedRole;

  UserFormState({
    required this.message,
    required this.isLoading,
    required this.selectedRole,
  });

  factory UserFormState.initial() {
    return UserFormState(message: '', isLoading: false, selectedRole: '');
  }

  UserFormState copyWith({
    String? message,
    bool? isLoading,
    String? selectedRole,
  }) {
    return UserFormState(
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      selectedRole: selectedRole ?? this.selectedRole,
    );
  }
}
