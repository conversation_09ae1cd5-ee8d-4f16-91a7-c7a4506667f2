part of 'user_cubit.dart';

@immutable
class UserState {
  final List<UserModel> users;
  final String message;
  final bool isLoading;

  UserState({
    required this.users,
    required this.message,
    required this.isLoading,
  });

  factory UserState.initial() {
    return UserState(users: [], message: '', isLoading: false);
  }

  UserState copyWith({
    List<UserModel>? users,
    UserModel? userDetail,
    String? message,
    bool? isLoading,
  }) {
    return UserState(
      users: users ?? this.users,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
