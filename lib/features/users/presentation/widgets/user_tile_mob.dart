import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_form_cubit.dart';
import 'package:cp_associates/features/users/presentation/widgets/editprofile_form.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UserTileMob extends StatelessWidget {
  const UserTileMob({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserCubit, UserState>(
      builder: (context, state) {
        if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        }
        return SingleChildScrollView(
          child: Column(
            children: [
              ...List.generate(state.users.length, (index) {
                final user = state.users[index];

                return InkWell(
                  onDoubleTap: () {
                    showModalBottomSheet(
                      isScrollControlled: true,
                      useSafeArea: true,
                      context: context,
                      builder: (context) {
                        return BlocProvider(
                          create:
                              (context) => UserFormCubit(
                                context.read<AuthCubit>().authRepo,
                              ),
                          child: Padding(
                            padding: MediaQuery.of(context).viewInsets,
                            child: EditProfileForm(editUser: user),
                          ),
                        );
                      },
                    );
                  },
                  onTap: () {
                    // context.push("${Routes.attendanceDetail}/${user.docId}");
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 15),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 18,
                        horizontal: 10,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.borderGrey),
                        borderRadius: BorderRadius.circular(10),
                        color: AppColors.containerGreyColor,
                      ),
                      child: Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: getColorFromInput(user.name[0]),
                            radius: 20,
                            child: Text(
                              user.name[0].toUpperCase(),
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),

                          SizedBox(width: 10),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text("${user.name} (${user.role})"),
                              SizedBox(height: 5),
                              Text(user.email),
                            ],
                          ),

                          Spacer(),
                          Row(
                            children: [
                              InkWell(
                                onTap: () {
                                  showModalBottomSheet(
                                    isScrollControlled: true,
                                    useSafeArea: true,
                                    context: context,
                                    builder: (context) {
                                      return BlocProvider(
                                        create:
                                            (context) => UserFormCubit(
                                              context
                                                  .read<AuthCubit>()
                                                  .authRepo,
                                            ),
                                        child: Padding(
                                          padding:
                                              MediaQuery.of(context).viewInsets,
                                          child: EditProfileForm(
                                            editUser: user,
                                          ),
                                        ),
                                      );
                                    },
                                  );
                                },
                                child: Icon(CupertinoIcons.pen, size: 20),
                              ),
                              SizedBox(width: 10),

                              InkWell(
                                onTap: () {
                                  showUserConfirmDeletDialog(context, () {
                                    context.read<UserCubit>().deleteUser(
                                      user.docId,
                                    );
                                  });
                                },
                                child: Icon(CupertinoIcons.delete, size: 20),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        );
      },
    );
  }
}
