<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>97591535-64D5-44B3-AAD8-E084CE4351FF</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>97591535-64D5-44B3-AAD8-E084CE4351FF.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>Runner project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>Runner</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning workspace Runner with scheme Runner</string>
			<key>timeStartedRecording</key>
			<real>773041452.26783895</real>
			<key>timeStoppedRecording</key>
			<real>773041452.52707696</real>
			<key>title</key>
			<string>Cleaning workspace Runner with scheme Runner</string>
			<key>uniqueIdentifier</key>
			<string>97591535-64D5-44B3-AAD8-E084CE4351FF</string>
		</dict>
	</dict>
</dict>
</plist>
